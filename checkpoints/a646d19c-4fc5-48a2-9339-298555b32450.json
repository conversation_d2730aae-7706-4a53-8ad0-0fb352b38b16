{"session_id": "", "task_id": "a646d19c-4fc5-48a2-9339-298555b32450", "state": {"state": {"current_task": null, "working_directory": null, "open_files": {}, "task_context": {"task_type": "General", "requirements": [], "constraints": [], "progress": {"completed_steps": [], "current_step": null, "remaining_steps": [], "completion_percentage": 0.0}, "related_files": []}, "session_history": []}, "current": 0, "messages": [{"HumanMessage": "hello"}], "error": null, "turns": 0}, "graph_state": {"next_node_name": "framework::graph::buildin_handlers::start::GraphStartHandler", "next_node_output": null}, "interrupt": null, "wake_up": null}