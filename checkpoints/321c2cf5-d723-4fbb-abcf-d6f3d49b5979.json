{"session_id": "", "task_id": "321c2cf5-d723-4fbb-abcf-d6f3d49b5979", "state": {"state": {"current_task": null, "working_directory": null, "open_files": {}, "task_context": {"task_type": "General", "requirements": [], "constraints": [], "progress": {"completed_steps": [], "current_step": null, "remaining_steps": [], "completion_percentage": 0.0}, "related_files": []}, "session_history": []}, "current": 0, "messages": [{"HumanMessage": "hello world"}], "error": null, "turns": 0}, "graph_state": {"next_node_name": "framework::graph::buildin_handlers::start::GraphStartHandler", "next_node_output": null}, "interrupt": null, "wake_up": null}