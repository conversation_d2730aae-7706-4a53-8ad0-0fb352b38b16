# Framework Migration Plan

## 架构重构计划

### 当前架构 vs Framework 架构

#### 当前架构问题
1. **单体设计**: Agent 控制器包含所有逻辑，难以扩展
2. **状态管理混乱**: 状态分散在多个组件中
3. **工具系统耦合**: 工具执行与 Agent 逻辑紧耦合
4. **缺乏流程控制**: 没有清晰的执行流程定义

#### Framework 架构优势
1. **图驱动执行**: 基于状态图的清晰执行流程
2. **模块化设计**: Agent、Tool、LLM 等组件解耦
3. **状态管理**: 统一的状态管理和持久化机制
4. **可扩展性**: 易于添加新的状态处理器和工具

### 新架构设计

#### 1. Agent 层次结构
```
CodeAssistantAgent (ReActAgent<Deepseek, CodeAssistantState, FileCheckPointer>)
├── States:
│   ├── IdleState (等待用户输入)
│   ├── PlanningState (分析任务，决定下一步)
│   ├── ToolCallState (执行工具调用)
│   ├── CodeEditState (专门处理代码编辑)
│   └── ShutdownState (结束任务)
└── Tools:
    ├── FileEditTool (创建、修改、删除文件)
    ├── DirectoryTool (目录操作)
    ├── SearchTool (代码搜索)
    └── AnalysisTool (代码分析)
```

#### 2. 状态定义
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeAssistantState {
    pub current_task: Option<String>,
    pub working_directory: Option<PathBuf>,
    pub open_files: HashMap<PathBuf, String>,
    pub task_context: TaskContext,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskContext {
    pub task_type: TaskType,
    pub requirements: Vec<String>,
    pub constraints: Vec<String>,
    pub progress: TaskProgress,
}
```

#### 3. 状态处理器
- **IdleStateHandler**: 处理初始状态和用户输入
- **PlanningStateHandler**: 分析任务，调用 LLM 制定计划
- **ToolCallStateHandler**: 执行工具调用
- **CodeEditStateHandler**: 专门处理代码编辑任务
- **ShutdownStateHandler**: 清理和结束

#### 4. 工具系统重构
将现有工具适配到 Framework 的 Tool trait：
```rust
pub struct FileEditTool;

#[async_trait]
impl Tool for FileEditTool {
    type Params = FileEditParams;
    type Result = FileEditResult;
    
    fn name(&self) -> Cow<str> { "file_edit".into() }
    fn description(&self) -> Cow<str> { "Create, modify, or delete files".into() }
    
    async fn call(&self, param: Option<Self::Params>) -> ToolResult<Self::Result> {
        // 实现文件编辑逻辑
    }
}
```

#### 5. 图工厂实现
```rust
pub struct CodeAssistantGraph;

impl<C> GraphBuilder<RunState<CodeAssistantState>, C> for CodeAssistantGraph
where
    C: AgentContext,
{
    fn build_graph(self) -> Result<CompiledGraph<RunState<CodeAssistantState>, CallingContext<C>>, GraphError> {
        let mut graph = StateGraph::new();
        
        // 添加状态节点
        let idle = graph.add_node(IdleStateHandler);
        let planning = graph.add_node(PlanningStateHandler);
        let tool_call = graph.add_node(ToolCallStateHandler);
        let code_edit = graph.add_node(CodeEditStateHandler);
        let shutdown = graph.add_node(ShutdownStateHandler);
        
        // 定义状态转换
        graph.connect(idle, planning);
        graph.add_conditional_edges(/* 路由逻辑 */);
        
        graph.compile()
    }
}
```

### 迁移步骤

1. **更新依赖**: 添加 framework crate 依赖
2. **实现状态类型**: 定义 CodeAssistantState 和相关类型
3. **实现状态处理器**: 逐个实现各状态的处理逻辑
4. **适配工具系统**: 将现有工具适配到 Framework Tool trait
5. **实现图工厂**: 定义状态转换逻辑
6. **更新 CLI 集成**: 修改 CLI 以使用新的 Agent 系统
7. **测试验证**: 确保所有功能正常工作

### 预期收益

1. **更清晰的执行流程**: 基于图的状态机提供清晰的执行路径
2. **更好的可维护性**: 模块化设计便于维护和扩展
3. **更强的可扩展性**: 易于添加新的状态和工具
4. **更好的错误处理**: Framework 提供统一的错误处理机制
5. **持久化支持**: 内置的检查点机制支持任务恢复
