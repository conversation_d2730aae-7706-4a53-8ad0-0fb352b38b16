use clap::{Arg, Command};
use minimal_agent::{
    cli::CliUI,
    config::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    agent::framework_agent::CodeAssistantAgentBuilder,
};
use tracing::info;
use dotenv::dotenv;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载环境变量
    dotenv().ok();

    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter("minimal_agent=info")
        .init();

    // 解析命令行参数
    let matches = Command::new("Minimal Agent")
        .version("0.1.0")
        .about("A minimal agent system based on LLM-driven tool calling")
        .arg(
            Arg::new("api-key")
                .long("api-key")
                .value_name("KEY")
                .help("API key (OpenAI or OpenRouter)")
                .required(false),
        )
        .arg(
            Arg::new("provider")
                .long("provider")
                .value_name("PROVIDER")
                .help("LLM provider (openai, openrouter)")
                .default_value("openrouter"),
        )
        .arg(
            Arg::new("model")
                .long("model")
                .value_name("MODEL")
                .help("LLM model to use")
                .default_value("anthropic/claude-3.5-sonnet"),
        )
        .arg(
            Arg::new("interactive")
                .short('i')
                .long("interactive")
                .help("Run in interactive mode")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("test-mode")
                .long("test-mode")
                .help("Use cheaper model for testing (Claude-3 Haiku)")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("coding-mode")
                .long("coding-mode")
                .help("Enable coding assistant mode with specialized tools and prompts")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("clear-config")
                .long("clear-config")
                .help("清除缓存的配置")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("show-config")
                .long("show-config")
                .help("显示配置信息")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("reconfigure")
                .long("reconfigure")
                .help("重新配置（清除缓存并重新设置）")
                .action(clap::ArgAction::SetTrue),
        )
        .get_matches();

    // 处理配置管理命令
    if matches.get_flag("clear-config") {
        ConfigManager::clear_cached_config()?;
        return Ok(());
    }

    if matches.get_flag("show-config") {
        ConfigManager::show_config_stats()?;
        return Ok(());
    }

    if matches.get_flag("reconfigure") {
        let test_mode = matches.get_flag("test-mode");
        let coding_mode = matches.get_flag("coding-mode");
        let config = ConfigManager::reconfigure(test_mode, coding_mode).await?;
        println!("✅ 重新配置完成！");
        ConfigManager::display_config(&config);
        return Ok(());
    }

    // 获取配置参数
    let provider = matches.get_one::<String>("provider").unwrap().clone();
    let mut model = matches.get_one::<String>("model").unwrap().clone();
    let coding_mode = matches.get_flag("coding-mode");
    let test_mode = matches.get_flag("test-mode");

    // 如果启用测试模式，使用更便宜的模型
    if test_mode {
        model = match provider.as_str() {
            "openrouter" => "anthropic/claude-3-haiku".to_string(),
            "openai" => "gpt-3.5-turbo".to_string(),
            _ => model,
        };
        println!("🧪 测试模式: 使用模型 {}", model);
    }

    // 使用配置管理器获取配置
    let config = ConfigManager::get_config(
        Some(provider),
        Some(model),
        matches.get_one::<String>("api-key").cloned(),
        test_mode,
        coding_mode,
    ).await?;

    // 验证配置
    if let Err(e) = ConfigManager::validate_config(&config) {
        eprintln!("❌ 配置错误: {}", e);
        return Err(e.into());
    }

    // 显示配置信息
    ConfigManager::display_config(&config);

    // 使用基于 framework 的 Agent 系统
    info!("🚀 Using framework-based agent system");

    // 创建基于 framework 的 Agent
    let agent = match CodeAssistantAgentBuilder::build_default() {
        Ok(agent) => agent,
        Err(e) => {
            eprintln!("Failed to create framework agent: {}", e);
            return Err(format!("Agent creation failed: {}", e).into());
        }
    };

    // 创建 CLI UI 并运行
    let mut cli_ui = CliUI::new();

    // 根据命令行参数决定运行模式
    if matches.get_flag("interactive") {
        cli_ui.run_framework_interactive_mode(agent).await?;
    } else {
        cli_ui.run_framework_single_query_mode(agent).await?;
    }

    Ok(())
}
