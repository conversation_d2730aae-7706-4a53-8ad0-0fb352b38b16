use enum_map::{enum_map, Enum};
use framework::{
    agent::{
        builder::GraphBuilder,
        context::CallingContext,
        state::RunState,
    },
    core::agent::AgentContext,
    graph::{router::IntoRouteValue, CompiledGraph, GraphError, StateGraph},
};

use crate::agent::{
    framework_state::{CodeAssistantState, AgentDecision, ToolCallDecision},
    handlers::{<PERSON>dleStateHand<PERSON>, PlanningStateHandler, ToolCallStateHandler, ShutdownStateHandler},
};

/// 代码助手图工厂
pub struct CodeAssistantGraph;

impl<C> GraphBuilder<RunState<CodeAssistantState>, C> for CodeAssistantGraph
where
    C: AgentContext,
{
    fn build_graph(
        self,
    ) -> Result<CompiledGraph<RunState<CodeAssistantState>, CallingContext<C>>, GraphError> {
        let mut graph = StateGraph::new();

        // 添加状态节点
        let idle = graph.add_node(IdleStateHandler);
        let planning = graph.add_node(PlanningStateHandler);
        let tool_call = graph.add_node(ToolCallStateHandler);
        let shutdown = graph.add_node(ShutdownStateHandler);

        // 定义路由枚举
        #[derive(Clone, Enum)]
        enum Route {
            Idle,
            Planning,
            ToolCall,
            Shutdown,
        }

        // 连接基本流程：idle -> planning
        graph.connect(idle, planning);

        // 从 planning 状态的条件路由
        graph.add_conditional_edges(
            "planning_decision".into(),
            planning,
            move |_run_state: &mut RunState<CodeAssistantState>, input: AgentDecision| {
                match input {
                    AgentDecision::Continue => (Route::Planning, ()).into_route_value(),
                    AgentDecision::CallTool(tool_decision) => {
                        (Route::ToolCall, tool_decision).into_route_value()
                    }
                    AgentDecision::TaskComplete(message) => {
                        (Route::Shutdown, AgentDecision::TaskComplete(message)).into_route_value()
                    }
                    AgentDecision::NeedUserInput(_) => (Route::Idle, ()).into_route_value(),
                    AgentDecision::HandleError(error) => {
                        (Route::Shutdown, AgentDecision::HandleError(error)).into_route_value()
                    }
                    AgentDecision::Shutdown => {
                        (Route::Shutdown, AgentDecision::Shutdown).into_route_value()
                    }
                }
            },
            enum_map! {
                Route::Idle => idle,
                Route::Planning => planning,
                Route::ToolCall => tool_call,
                Route::Shutdown => shutdown,
            },
        );

        // 从 tool_call 状态的条件路由
        graph.add_conditional_edges(
            "tool_call_decision".into(),
            tool_call,
            move |_run_state: &mut RunState<CodeAssistantState>, input: AgentDecision| {
                match input {
                    AgentDecision::Continue => (Route::Planning, ()).into_route_value(),
                    AgentDecision::TaskComplete(message) => {
                        (Route::Shutdown, AgentDecision::TaskComplete(message)).into_route_value()
                    }
                    AgentDecision::HandleError(error) => {
                        (Route::Shutdown, AgentDecision::HandleError(error)).into_route_value()
                    }
                    AgentDecision::NeedUserInput(_) => (Route::Idle, ()).into_route_value(),
                    _ => (Route::Planning, ()).into_route_value(),
                }
            },
            enum_map! {
                Route::Idle => idle,
                Route::Planning => planning,
                Route::ToolCall => tool_call,
                Route::Shutdown => shutdown,
            },
        );

        // 编译图
        graph.compile()
    }
}
