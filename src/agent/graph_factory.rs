use framework::{
    agent::{
        builder::Graph<PERSON>uilder,
        context::CallingContext,
        state::RunState,
    },
    core::agent::AgentContext,
    graph::{CompiledGraph, GraphError, StateGraph},
};

use crate::agent::{
    state::CodeAssistantState,
    handlers::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PlanningStateHandler, ToolCallStateHandler, ShutdownStateHandler},
};

/// 代码助手图工厂
pub struct CodeAssistantGraph;

impl<C> GraphBuilder<RunState<CodeAssistantState>, C> for CodeAssistantGraph
where
    C: AgentContext,
{
    fn build_graph(
        self,
    ) -> Result<CompiledGraph<RunState<CodeAssistantState>, CallingContext<C>>, GraphError> {
        let mut graph = StateGraph::new();

        // 添加状态节点
        let idle = graph.add_node(IdleStateHandler);
        let planning = graph.add_node(PlanningStateHandler);
        let tool_call = graph.add_node(ToolCallStateHandler);
        let shutdown = graph.add_node(ShutdownStateHandler);

        // 连接基本流程
        graph.connect(idle, planning);
        graph.connect(planning, tool_call);
        graph.connect(tool_call, planning);
        graph.connect(planning, shutdown);
        graph.connect(tool_call, shutdown);

        // 编译图
        graph.compile()
    }
}
