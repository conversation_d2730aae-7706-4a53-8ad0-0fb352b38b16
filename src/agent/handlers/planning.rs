use async_trait::async_trait;
use framework::{
    agent::{
        context::CallingContext,
        state::RunState,
    },
    core::{
        agent::AgentContext,
        error::AgentError,
        handler::StageHandler,
        llm::LLM,
        message::Message,
    },
};

use crate::agent::framework_state::{CodeAssistantState, AgentDecision, ToolCallDecision, TaskType};

/// 规划状态处理器 - 分析任务并制定计划
#[derive(Debug)]
pub struct PlanningStateHandler;

#[async_trait]
impl<C: AgentContext> StageHandler<RunState<CodeAssistantState>, CallingContext<C>> for PlanningStateHandler {
    type Incoming = ();
    type Outgoing = AgentDecision;

    async fn handle(
        &self,
        ctx: CallingContext<C>,
        state: &mut RunState<CodeAssistantState>,
        _input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        let llm = ctx.llm()?;
        state.turns += 1;

        // 构建系统提示
        let system_prompt = self.build_system_prompt(&state.state);
        
        // 构建消息列表
        let mut messages = Vec::new();
        messages.push(Message::new_system_message(system_prompt));
        messages.extend_from_slice(state.messages());

        // 调用 LLM 进行规划
        let response = llm
            .generate(&messages)
            .await
            .map_err(|e| AgentError::InternalError(e.to_string()))?;

        // 添加 AI 响应到消息历史
        let mut ai_message = Message::new_ai_message(response.generation().into());
        ai_message.with_tool_calls(response.tool_calls().map(|calls| calls.to_vec()));
        state.add_message(ai_message);

        // 分析响应并决定下一步
        if let Some(tool_calls) = response.tool_calls() {
            if let Some(tool_call) = tool_calls.first() {
                // 有工具调用，转到工具调用状态
                let decision = ToolCallDecision {
                    tool_name: tool_call.function.name.clone(),
                    parameters: tool_call.function.arguments.clone(),
                    reason: format!("LLM requested to call tool: {}", tool_call.function.name),
                };
                
                return Ok(AgentDecision::CallTool(decision));
            }
        }

        // 检查是否任务完成
        let response_text = response.generation();
        if self.is_task_complete(&response_text) {
            return Ok(AgentDecision::TaskComplete(response_text.to_string()));
        }

        // 检查是否需要更多信息
        if self.needs_more_info(&response_text) {
            return Ok(AgentDecision::NeedUserInput(
                "I need more information to proceed. Could you provide more details?".to_string()
            ));
        }

        // 继续规划
        Ok(AgentDecision::Continue)
    }
}

impl PlanningStateHandler {
    /// 构建系统提示
    fn build_system_prompt(&self, state: &CodeAssistantState) -> String {
        let mut prompt = String::from(
            "You are a helpful code assistant. Your goal is to help users with their coding tasks.\n\n"
        );

        // 添加当前状态信息
        if let Some(task) = &state.current_task {
            prompt.push_str(&format!("Current task: {}\n", task));
        }

        if let Some(dir) = &state.working_directory {
            prompt.push_str(&format!("Working directory: {}\n", dir.display()));
        }

        if !state.open_files.is_empty() {
            prompt.push_str("Open files:\n");
            for (path, _) in &state.open_files {
                prompt.push_str(&format!("- {}\n", path.display()));
            }
        }

        // 添加任务上下文
        if !state.task_context.requirements.is_empty() {
            prompt.push_str("\nRequirements:\n");
            for req in &state.task_context.requirements {
                prompt.push_str(&format!("- {}\n", req));
            }
        }

        if !state.task_context.constraints.is_empty() {
            prompt.push_str("\nConstraints:\n");
            for constraint in &state.task_context.constraints {
                prompt.push_str(&format!("- {}\n", constraint));
            }
        }

        prompt.push_str("\nAvailable tools:\n");
        prompt.push_str("- file_edit: Create, modify, or delete files\n");
        prompt.push_str("- directory_ops: List directories, create folders\n");
        prompt.push_str("- code_search: Search for code patterns\n");
        prompt.push_str("- code_analysis: Analyze code structure and quality\n");

        prompt.push_str("\nPlease analyze the user's request and decide what action to take. ");
        prompt.push_str("If you need to use tools, call them with appropriate parameters. ");
        prompt.push_str("If the task is complete, provide a summary. ");
        prompt.push_str("If you need more information, ask clarifying questions.");

        prompt
    }

    /// 检查任务是否完成
    fn is_task_complete(&self, response: &str) -> bool {
        let completion_indicators = [
            "task completed",
            "task finished",
            "done",
            "completed successfully",
            "finished successfully",
            "task is complete",
        ];

        let response_lower = response.to_lowercase();
        completion_indicators.iter().any(|indicator| response_lower.contains(indicator))
    }

    /// 检查是否需要更多信息
    fn needs_more_info(&self, response: &str) -> bool {
        let info_indicators = [
            "need more information",
            "could you provide",
            "please clarify",
            "can you tell me",
            "what do you mean",
            "more details",
        ];

        let response_lower = response.to_lowercase();
        info_indicators.iter().any(|indicator| response_lower.contains(indicator))
    }
}
