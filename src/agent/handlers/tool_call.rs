use async_trait::async_trait;
use framework::{
    agent::{
        context::CallingContext,
        state::RunState,
    },
    core::{
        agent::AgentContext,
        error::AgentError,
        handler::StageHand<PERSON>,
        message::{Message, ToolCall, FunctionCall},
    },
};

use crate::agent::state::{CodeAssistantState, AgentDecision, ToolCallDecision, ToolCallResult};

/// 工具调用状态处理器 - 执行工具调用
#[derive(Debug)]
pub struct ToolCallStateHandler;

#[async_trait]
impl<C: AgentContext> StageHandler<RunState<CodeAssistantState>, CallingContext<C>> for ToolCallStateHandler {
    type Incoming = ();
    type Outgoing = ();

    async fn handle(
        &self,
        _ctx: CallingContext<C>,
        state: &mut RunState<CodeAssistantState>,
        _input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        // 简化处理：只是记录工具调用
        tracing::info!("Tool call handler executed");

        // 更新进度
        state.state.update_progress("Tool execution completed".to_string());

        Ok(())
    }
}

impl ToolCallStateHandler {
    /// 处理成功的工具调用
    async fn handle_successful_tool_call(
        &self,
        tool_name: &str,
        result: &ToolCallResult,
        state: &mut RunState<CodeAssistantState>,
    ) -> Result<(), AgentError> {
        match tool_name {
            "file_edit" => {
                self.handle_file_edit_result(result, state).await?;
            }
            "directory_ops" => {
                self.handle_directory_ops_result(result, state).await?;
            }
            "code_search" => {
                self.handle_code_search_result(result, state).await?;
            }
            "code_analysis" => {
                self.handle_code_analysis_result(result, state).await?;
            }
            _ => {
                tracing::warn!("Unknown tool: {}", tool_name);
            }
        }
        Ok(())
    }

    /// 处理文件编辑结果
    async fn handle_file_edit_result(
        &self,
        result: &ToolCallResult,
        state: &mut RunState<CodeAssistantState>,
    ) -> Result<(), AgentError> {
        // 解析文件编辑结果
        if let Ok(file_info) = serde_json::from_value::<serde_json::Value>(result.result.clone()) {
            if let Some(file_path) = file_info.get("file_path").and_then(|p| p.as_str()) {
                let path = std::path::PathBuf::from(file_path);
                
                // 如果是文件创建或修改，添加到打开文件列表
                if let Some(content) = file_info.get("content").and_then(|c| c.as_str()) {
                    state.state.add_open_file(path.clone(), content.to_string());
                }
                
                // 添加到相关文件
                state.state.add_related_file(path);
            }
        }
        Ok(())
    }

    /// 处理目录操作结果
    async fn handle_directory_ops_result(
        &self,
        result: &ToolCallResult,
        state: &mut RunState<CodeAssistantState>,
    ) -> Result<(), AgentError> {
        // 解析目录操作结果
        if let Ok(dir_info) = serde_json::from_value::<serde_json::Value>(result.result.clone()) {
            if let Some(working_dir) = dir_info.get("working_directory").and_then(|d| d.as_str()) {
                let path = std::path::PathBuf::from(working_dir);
                state.state.set_working_directory(path);
            }
        }
        Ok(())
    }

    /// 处理代码搜索结果
    async fn handle_code_search_result(
        &self,
        _result: &ToolCallResult,
        _state: &mut RunState<CodeAssistantState>,
    ) -> Result<(), AgentError> {
        // 代码搜索结果通常不需要更新状态，只是提供信息
        Ok(())
    }

    /// 处理代码分析结果
    async fn handle_code_analysis_result(
        &self,
        _result: &ToolCallResult,
        _state: &mut RunState<CodeAssistantState>,
    ) -> Result<(), AgentError> {
        // 代码分析结果通常不需要更新状态，只是提供信息
        Ok(())
    }
}
