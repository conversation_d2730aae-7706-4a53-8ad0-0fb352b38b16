use async_trait::async_trait;
use framework::{
    agent::{
        context::CallingContext,
        state::RunState,
    },
    core::{
        agent::AgentContext,
        error::AgentError,
        handler::<PERSON><PERSON><PERSON><PERSON>,
        message::Message,
    },
};

use crate::agent::framework_state::{CodeAssistantState, AgentDecision, ToolCallDecision, ToolCallResult};

/// 工具调用状态处理器 - 执行工具调用
#[derive(Debug)]
pub struct ToolCallStateHandler;

#[async_trait]
impl<C: AgentContext> StageHandler<RunState<CodeAssistantState>, CallingContext<C>> for ToolCallStateHandler {
    type Incoming = ToolCallDecision;
    type Outgoing = AgentDecision;

    async fn handle(
        &self,
        ctx: CallingContext<C>,
        state: &mut RunState<CodeAssistantState>,
        input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        tracing::info!("Executing tool call: {} with parameters: {:?}", input.tool_name, input.parameters);

        // 更新进度
        state.state.update_progress(format!("Executing tool: {}", input.tool_name));

        // 执行工具调用
        let tool_result = match ctx.call_tool(input.tool_name.clone(), input.parameters.clone()).await {
            Ok(response) => {
                if response.code == 0 {
                    ToolCallResult {
                        tool_name: input.tool_name.clone(),
                        success: true,
                        result: response.data.unwrap_or(serde_json::Value::Null),
                        error_message: None,
                    }
                } else {
                    ToolCallResult {
                        tool_name: input.tool_name.clone(),
                        success: false,
                        result: serde_json::Value::Null,
                        error_message: response.error_message,
                    }
                }
            }
            Err(e) => {
                ToolCallResult {
                    tool_name: input.tool_name.clone(),
                    success: false,
                    result: serde_json::Value::Null,
                    error_message: Some(e.to_string()),
                }
            }
        };

        // 创建工具消息并添加到历史
        let tool_message_content = if tool_result.success {
            format!("Tool '{}' executed successfully. Result: {}", 
                   tool_result.tool_name, 
                   serde_json::to_string_pretty(&tool_result.result).unwrap_or_default())
        } else {
            format!("Tool '{}' failed. Error: {}", 
                   tool_result.tool_name, 
                   tool_result.error_message.unwrap_or_default())
        };

        let tool_message = Message::new_tool_message(tool_message_content, "tool_call_id".to_string());
        state.add_message(tool_message);

        // 根据工具执行结果决定下一步
        if tool_result.success {
            // 工具执行成功，根据工具类型进行后续处理
            self.handle_successful_tool_call(&input.tool_name, &tool_result, state).await?;
            
            // 返回继续规划，让 LLM 分析工具结果
            Ok(AgentDecision::Continue)
        } else {
            // 工具执行失败，记录错误并继续
            let error_msg = format!("Tool execution failed: {}", 
                                   tool_result.error_message.unwrap_or_default());
            tracing::error!("{}", error_msg);
            
            // 可以选择重试或者继续规划
            Ok(AgentDecision::HandleError(error_msg))
        }
    }
}

impl ToolCallStateHandler {
    /// 处理成功的工具调用
    async fn handle_successful_tool_call(
        &self,
        tool_name: &str,
        result: &ToolCallResult,
        state: &mut RunState<CodeAssistantState>,
    ) -> Result<(), AgentError> {
        match tool_name {
            "file_edit" => {
                self.handle_file_edit_result(result, state).await?;
            }
            "directory_ops" => {
                self.handle_directory_ops_result(result, state).await?;
            }
            "code_search" => {
                self.handle_code_search_result(result, state).await?;
            }
            "code_analysis" => {
                self.handle_code_analysis_result(result, state).await?;
            }
            _ => {
                tracing::warn!("Unknown tool: {}", tool_name);
            }
        }
        Ok(())
    }

    /// 处理文件编辑结果
    async fn handle_file_edit_result(
        &self,
        result: &ToolCallResult,
        state: &mut RunState<CodeAssistantState>,
    ) -> Result<(), AgentError> {
        // 解析文件编辑结果
        if let Ok(file_info) = serde_json::from_value::<serde_json::Value>(result.result.clone()) {
            if let Some(file_path) = file_info.get("file_path").and_then(|p| p.as_str()) {
                let path = std::path::PathBuf::from(file_path);
                
                // 如果是文件创建或修改，添加到打开文件列表
                if let Some(content) = file_info.get("content").and_then(|c| c.as_str()) {
                    state.state.add_open_file(path.clone(), content.to_string());
                }
                
                // 添加到相关文件
                state.state.add_related_file(path);
            }
        }
        Ok(())
    }

    /// 处理目录操作结果
    async fn handle_directory_ops_result(
        &self,
        result: &ToolCallResult,
        state: &mut RunState<CodeAssistantState>,
    ) -> Result<(), AgentError> {
        // 解析目录操作结果
        if let Ok(dir_info) = serde_json::from_value::<serde_json::Value>(result.result.clone()) {
            if let Some(working_dir) = dir_info.get("working_directory").and_then(|d| d.as_str()) {
                let path = std::path::PathBuf::from(working_dir);
                state.state.set_working_directory(path);
            }
        }
        Ok(())
    }

    /// 处理代码搜索结果
    async fn handle_code_search_result(
        &self,
        _result: &ToolCallResult,
        _state: &mut RunState<CodeAssistantState>,
    ) -> Result<(), AgentError> {
        // 代码搜索结果通常不需要更新状态，只是提供信息
        Ok(())
    }

    /// 处理代码分析结果
    async fn handle_code_analysis_result(
        &self,
        _result: &ToolCallResult,
        _state: &mut RunState<CodeAssistantState>,
    ) -> Result<(), AgentError> {
        // 代码分析结果通常不需要更新状态，只是提供信息
        Ok(())
    }
}
