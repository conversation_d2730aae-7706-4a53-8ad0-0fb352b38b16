use async_trait::async_trait;
use framework::{
    agent::{
        context::CallingContext,
        state::RunState,
    },
    core::{
        agent::AgentContext,
        error::AgentError,
        handler::<PERSON><PERSON>and<PERSON>,
        event::Event,
    },
};

use crate::agent::framework_state::{CodeAssistantState, AgentDecision};

/// 关闭状态处理器 - 清理和结束任务
#[derive(Debug)]
pub struct ShutdownStateHandler;

#[async_trait]
impl<C: AgentContext> StageHandler<RunState<CodeAssistantState>, CallingContext<C>> for ShutdownStateHandler {
    type Incoming = AgentDecision;
    type Outgoing = String;

    async fn handle(
        &self,
        ctx: CallingContext<C>,
        state: &mut RunState<CodeAssistantState>,
        input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        let final_message = match input {
            AgentDecision::TaskComplete(message) => {
                tracing::info!("Task completed successfully");
                state.state.update_progress("Task completed".to_string());
                
                // 生成任务完成报告
                let report = self.generate_completion_report(&state.state);
                format!("{}\n\n{}", message, report)
            }
            AgentDecision::HandleError(error) => {
                tracing::error!("Task ended with error: {}", error);
                format!("Task ended with error: {}", error)
            }
            AgentDecision::Shutdown => {
                tracing::info!("Agent shutdown requested");
                "Agent shutdown completed.".to_string()
            }
            _ => {
                tracing::warn!("Unexpected shutdown reason");
                "Task ended unexpectedly.".to_string()
            }
        };

        // 发送完成事件
        ctx.emit_message(Event::ProcessingComplete {
            output: framework::core::event::ProcessedOutput {
                payload: std::sync::Arc::from(final_message.clone()),
                history: state.messages().clone(),
            },
        });

        // 清理状态
        self.cleanup_state(state);

        Ok(final_message)
    }
}

impl ShutdownStateHandler {
    /// 生成任务完成报告
    fn generate_completion_report(&self, state: &CodeAssistantState) -> String {
        let mut report = String::from("## Task Completion Report\n\n");

        // 任务信息
        if let Some(task) = &state.current_task {
            report.push_str(&format!("**Task:** {}\n", task));
        }

        // 工作目录
        if let Some(dir) = &state.working_directory {
            report.push_str(&format!("**Working Directory:** {}\n", dir.display()));
        }

        // 处理的文件
        if !state.open_files.is_empty() {
            report.push_str("\n**Files Processed:**\n");
            for (path, _) in &state.open_files {
                report.push_str(&format!("- {}\n", path.display()));
            }
        }

        // 相关文件
        if !state.task_context.related_files.is_empty() {
            report.push_str("\n**Related Files:**\n");
            for file in &state.task_context.related_files {
                report.push_str(&format!("- {}\n", file.display()));
            }
        }

        // 任务进度
        let progress = &state.task_context.progress;
        if !progress.completed_steps.is_empty() {
            report.push_str("\n**Completed Steps:**\n");
            for (i, step) in progress.completed_steps.iter().enumerate() {
                report.push_str(&format!("{}. {}\n", i + 1, step));
            }
        }

        // 需求和约束
        if !state.task_context.requirements.is_empty() {
            report.push_str("\n**Requirements Met:**\n");
            for req in &state.task_context.requirements {
                report.push_str(&format!("- {}\n", req));
            }
        }

        if !state.task_context.constraints.is_empty() {
            report.push_str("\n**Constraints Followed:**\n");
            for constraint in &state.task_context.constraints {
                report.push_str(&format!("- {}\n", constraint));
            }
        }

        // 会话统计
        report.push_str(&format!("\n**Session Statistics:**\n"));
        report.push_str(&format!("- Total interactions: {}\n", state.session_history.len()));
        report.push_str(&format!("- Task completion: {:.1}%\n", progress.completion_percentage));

        report
    }

    /// 清理状态
    fn cleanup_state(&self, state: &mut RunState<CodeAssistantState>) {
        // 记录会话结束
        state.state.session_history.push("Session ended".to_string());
        
        // 可以在这里添加其他清理逻辑，比如：
        // - 保存会话历史到文件
        // - 清理临时文件
        // - 释放资源等
        
        tracing::info!("State cleanup completed");
    }
}
