use async_trait::async_trait;
use framework::{
    agent::{
        context::CallingContext,
        state::RunState,
    },
    core::{
        agent::AgentContext,
        error::AgentError,
        handler::<PERSON><PERSON><PERSON><PERSON>,
        message::Message,
    },
};

use crate::agent::framework_state::{CodeAssistantState, AgentDecision};

/// 空闲状态处理器 - 等待用户输入
#[derive(Debug)]
pub struct IdleStateHandler;

#[async_trait]
impl<C: AgentContext> StageHandler<RunState<CodeAssistantState>, CallingContext<C>> for IdleStateHandler {
    type Incoming = ();
    type Outgoing = ();

    async fn handle(
        &self,
        ctx: CallingContext<C>,
        state: &mut RunState<CodeAssistantState>,
        _input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        // 检查是否有新的用户消息
        let last_message_content = if let Some(last_message) = state.messages().last() {
            match last_message {
                Message::HumanMessage(content) => {
                    Some(content.clone())
                }
                _ => None
            }
        } else {
            None
        };

        if let Some(content) = last_message_content {
            // 设置当前任务
            state.state.set_current_task(content.clone());

            // 记录接收到用户输入
            tracing::info!("Received user input: {}", content);

            // 转到规划状态
            return Ok(());
        }

        // 如果没有用户输入，继续等待
        Ok(())
    }
}
