use std::sync::Arc;
use async_trait::async_trait;
use framework::{
    agent::agent::ReActAgent,
    core::{
        agent::Agent,
        checkpoint::{CheckPoint, CheckPointError, CheckPointer},
        task::TaskHandle,
    },
    language_models::deepseek::Deepseek,
    tool::tool_box::Toolbox,
    graph::builtin_action::interrupt::UserAction,
};
use serde::{Serialize, Deserialize};
use std::fmt::Debug;
use std::marker::PhantomData;

use crate::agent::{
    framework_state::CodeAssistantState,
    graph_factory::CodeAssistantGraph,
};
use crate::tools::framework_tools::{SimpleEchoTool, SimpleFileReadTool, SimpleFileWriteTool, SimpleListDirTool};

/// 文件检查点存储器
pub struct FileCheckPointer<T> {
    _phantom: PhantomData<T>,
}

impl<T> FileCheckPointer<T> {
    pub fn new() -> Self {
        Self {
            _phantom: PhantomData,
        }
    }
}

#[async_trait]
impl<T> CheckPointer<T> for FileCheckPointer<T>
where
    T: Debug + Clone + Sync + Send + 'static + Serialize + for<'de> Deserialize<'de>,
{
    async fn get(&self, session_id: &str) -> Result<CheckPoint<T>, CheckPointError> {
        // 创建检查点目录
        let checkpoint_dir = std::path::Path::new("./checkpoints");
        if !checkpoint_dir.exists() {
            std::fs::create_dir_all(checkpoint_dir)
                .map_err(|e| CheckPointError::IOError(e.to_string()))?;
        }

        let checkpoint_path = checkpoint_dir.join(format!("{}.json", session_id));
        let checkpoint_str = std::fs::read_to_string(checkpoint_path)
            .map_err(|e| CheckPointError::IOError(e.to_string()))?;
        
        let checkpoint: CheckPoint<T> = serde_json::from_str(&checkpoint_str)
            .map_err(|e| CheckPointError::IOError(format!("Serialization error: {}", e)))?;
        
        Ok(checkpoint)
    }

    async fn save(
        &self,
        session_id: &str,
        checkpoint: &CheckPoint<T>,
    ) -> Result<(), CheckPointError> {
        // 创建检查点目录
        let checkpoint_dir = std::path::Path::new("./checkpoints");
        if !checkpoint_dir.exists() {
            std::fs::create_dir_all(checkpoint_dir)
                .map_err(|e| CheckPointError::IOError(e.to_string()))?;
        }

        let checkpoint_path = checkpoint_dir.join(format!("{}.json", session_id));
        let checkpoint_str = serde_json::to_string_pretty(checkpoint)
            .map_err(|e| CheckPointError::IOError(format!("Serialization error: {}", e)))?;
        
        std::fs::write(checkpoint_path, checkpoint_str)
            .map_err(|e| CheckPointError::IOError(e.to_string()))?;
        
        tracing::info!("Checkpoint saved for session: {}", session_id);
        Ok(())
    }
}

/// 代码助手 Agent 类型别名
pub type CodeAssistantAgent = ReActAgent<Deepseek, CodeAssistantState, FileCheckPointer<CodeAssistantState>>;

/// 代码助手 Agent 构建器
pub struct CodeAssistantAgentBuilder;

impl CodeAssistantAgentBuilder {
    /// 创建新的代码助手 Agent
    pub fn new() -> Self {
        Self
    }

    /// 构建代码助手 Agent
    pub fn build(
        self,
        name: &str,
        instructions: &str,
        api_key: Option<String>,
    ) -> Result<Arc<CodeAssistantAgent>, Box<dyn std::error::Error + Send + Sync>> {
        // 创建工具箱
        let mut toolbox = Toolbox::new();
        toolbox.add_tool(SimpleEchoTool)?;
        toolbox.add_tool(SimpleFileReadTool)?;
        toolbox.add_tool(SimpleFileWriteTool)?;
        toolbox.add_tool(SimpleListDirTool)?;

        // 创建 LLM 客户端
        let mut deepseek = Deepseek::new();
        if let Some(key) = api_key {
            deepseek = deepseek.with_api_key(key);
        }
        deepseek = deepseek.with_json_mode(true);

        // 创建检查点存储器
        let checkpoint = FileCheckPointer::new();

        // 构建 Agent
        let agent = ReActAgent::builder()
            .with_name(name)
            .with_instruction(instructions)
            .with_handle_factory(CodeAssistantGraph)
            .with_tool_box(Arc::new(toolbox))
            .with_llm(deepseek)
            .with_check_pointer(checkpoint)
            .build()?;

        Ok(agent)
    }

    /// 构建默认的代码助手 Agent
    pub fn build_default() -> Result<Arc<CodeAssistantAgent>, Box<dyn std::error::Error + Send + Sync>> {
        let instructions = r#"
You are a helpful code assistant designed to help developers with various coding tasks.

## Your Capabilities

You can help with:
- **File Operations**: Create, read, update, delete, and append to files
- **Directory Operations**: List directory contents, create/delete directories
- **Code Search**: Search for patterns in code files with regex support
- **Code Analysis**: Analyze code statistics, complexity, structure, and quality

## Available Tools

1. **file_edit**: Create, modify, or delete files
   - Operations: create, read, update, delete, append
   - Supports backup for modifications

2. **directory_ops**: Manage directories and list contents
   - Operations: list, create, delete, exists, info
   - Supports recursive operations and file filtering

3. **code_search**: Search for patterns in code
   - Supports regex and case-sensitive search
   - File type filtering and context lines
   - Recursive directory search

4. **code_analysis**: Analyze code quality and structure
   - Statistics: line counts, language breakdown
   - Complexity: function complexity analysis
   - Structure: modules, classes, functions
   - Quality: issues and suggestions
   - Dependencies: external and internal dependencies

## Guidelines

- Always understand the user's request before taking action
- Use appropriate tools for the task at hand
- Provide clear explanations of what you're doing
- Ask for clarification if the request is ambiguous
- Be careful with destructive operations (delete, overwrite)
- Suggest best practices when relevant
- Provide helpful error messages if something goes wrong

## Working Style

1. **Analyze** the user's request carefully
2. **Plan** the steps needed to complete the task
3. **Execute** using the appropriate tools
4. **Verify** the results and provide feedback
5. **Suggest** improvements or next steps if relevant

Remember to be helpful, accurate, and safe in all operations.
"#;

        Self::new().build(
            "CodeAssistant",
            instructions,
            std::env::var("DEEPSEEK_API_KEY").ok(),
        )
    }
}

impl Default for CodeAssistantAgentBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 代码助手 Agent 的便捷函数
impl CodeAssistantAgentBuilder {
    /// 创建新的代码助手 Agent 实例
    pub fn new_code_assistant(
        name: &str,
        instructions: &str,
        api_key: Option<String>,
    ) -> Result<Arc<CodeAssistantAgent>, Box<dyn std::error::Error + Send + Sync>> {
        Self::new().build(name, instructions, api_key)
    }

    /// 创建默认的代码助手 Agent 实例
    pub fn new_default_agent() -> Result<Arc<CodeAssistantAgent>, Box<dyn std::error::Error + Send + Sync>> {
        Self::build_default()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_create_code_assistant() {
        let agent = CodeAssistantAgent::new_default();
        assert!(agent.is_ok());
    }

    #[test]
    fn test_file_checkpoint_creation() {
        let checkpoint = FileCheckPointer::<CodeAssistantState>::new();
        // 基本创建测试
        assert!(std::mem::size_of_val(&checkpoint) > 0);
    }
}
