use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;

/// 代码助手的状态信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct CodeAssistantState {
    /// 当前任务描述
    pub current_task: Option<String>,
    /// 工作目录
    pub working_directory: Option<PathBuf>,
    /// 打开的文件及其内容
    pub open_files: HashMap<PathBuf, String>,
    /// 任务上下文
    pub task_context: TaskContext,
    /// 会话历史
    pub session_history: Vec<String>,
}

/// 任务上下文信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct TaskContext {
    /// 任务类型
    pub task_type: TaskType,
    /// 任务需求
    pub requirements: Vec<String>,
    /// 约束条件
    pub constraints: Vec<String>,
    /// 任务进度
    pub progress: TaskProgress,
    /// 相关文件
    pub related_files: Vec<PathBuf>,
}

/// 任务类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub enum TaskType {
    #[default]
    General,
    FileEdit,
    CodeGeneration,
    CodeAnalysis,
    Debugging,
    Refactoring,
    Documentation,
}

/// 任务进度
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct TaskProgress {
    /// 已完成的步骤
    pub completed_steps: Vec<String>,
    /// 当前步骤
    pub current_step: Option<String>,
    /// 剩余步骤
    pub remaining_steps: Vec<String>,
    /// 完成百分比
    pub completion_percentage: f32,
}

impl CodeAssistantState {
    /// 创建新的状态
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置当前任务
    pub fn set_current_task(&mut self, task: String) {
        self.current_task = Some(task.clone());
        self.session_history.push(format!("Task started: {}", task));
    }

    /// 设置工作目录
    pub fn set_working_directory(&mut self, dir: PathBuf) {
        self.working_directory = Some(dir.clone());
        self.session_history.push(format!("Working directory set to: {}", dir.display()));
    }

    /// 添加打开的文件
    pub fn add_open_file(&mut self, path: PathBuf, content: String) {
        self.open_files.insert(path.clone(), content);
        self.session_history.push(format!("File opened: {}", path.display()));
    }

    /// 移除打开的文件
    pub fn remove_open_file(&mut self, path: &PathBuf) {
        if self.open_files.remove(path).is_some() {
            self.session_history.push(format!("File closed: {}", path.display()));
        }
    }

    /// 更新任务进度
    pub fn update_progress(&mut self, step: String) {
        if let Some(current) = &self.task_context.progress.current_step {
            self.task_context.progress.completed_steps.push(current.clone());
        }
        self.task_context.progress.current_step = Some(step.clone());
        self.session_history.push(format!("Progress updated: {}", step));
        
        // 更新完成百分比
        let total_steps = self.task_context.progress.completed_steps.len() 
            + if self.task_context.progress.current_step.is_some() { 1 } else { 0 }
            + self.task_context.progress.remaining_steps.len();
        
        if total_steps > 0 {
            self.task_context.progress.completion_percentage = 
                (self.task_context.progress.completed_steps.len() as f32 / total_steps as f32) * 100.0;
        }
    }

    /// 添加需求
    pub fn add_requirement(&mut self, requirement: String) {
        self.task_context.requirements.push(requirement.clone());
        self.session_history.push(format!("Requirement added: {}", requirement));
    }

    /// 添加约束
    pub fn add_constraint(&mut self, constraint: String) {
        self.task_context.constraints.push(constraint.clone());
        self.session_history.push(format!("Constraint added: {}", constraint));
    }

    /// 添加相关文件
    pub fn add_related_file(&mut self, file: PathBuf) {
        if !self.task_context.related_files.contains(&file) {
            self.task_context.related_files.push(file.clone());
            self.session_history.push(format!("Related file added: {}", file.display()));
        }
    }

    /// 获取状态摘要
    pub fn get_summary(&self) -> String {
        let mut summary = String::new();
        
        if let Some(task) = &self.current_task {
            summary.push_str(&format!("Current Task: {}\n", task));
        }
        
        if let Some(dir) = &self.working_directory {
            summary.push_str(&format!("Working Directory: {}\n", dir.display()));
        }
        
        summary.push_str(&format!("Open Files: {}\n", self.open_files.len()));
        summary.push_str(&format!("Task Type: {:?}\n", self.task_context.task_type));
        summary.push_str(&format!("Progress: {:.1}%\n", self.task_context.progress.completion_percentage));
        
        if let Some(current_step) = &self.task_context.progress.current_step {
            summary.push_str(&format!("Current Step: {}\n", current_step));
        }
        
        summary
    }
}

impl TaskContext {
    /// 创建新的任务上下文
    pub fn new(task_type: TaskType) -> Self {
        Self {
            task_type,
            ..Default::default()
        }
    }

    /// 设置任务步骤
    pub fn set_steps(&mut self, steps: Vec<String>) {
        self.progress.remaining_steps = steps;
        self.progress.completion_percentage = 0.0;
    }
}

/// 决策类型，用于状态转换
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AgentDecision {
    /// 继续规划
    Continue,
    /// 调用工具
    CallTool(ToolCallDecision),
    /// 任务完成
    TaskComplete(String),
    /// 需要用户输入
    NeedUserInput(String),
    /// 错误处理
    HandleError(String),
    /// 关闭
    Shutdown,
}

/// 工具调用决策
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCallDecision {
    pub tool_name: String,
    pub parameters: serde_json::Value,
    pub reason: String,
}

/// 工具调用结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCallResult {
    pub tool_name: String,
    pub success: bool,
    pub result: serde_json::Value,
    pub error_message: Option<String>,
}
