use crate::agent::CodeAssistantAgent;
use crate::cli::input::InputHandler;
use crate::cli::display::CliDisplay;
use indicatif::{ProgressBar, ProgressStyle};
use std::sync::Arc;
use tokio_stream::StreamExt;
use framework::core::event::Event;
use framework::core::agent::Agent as FrameworkAgent;
use console::style;

/// CLI 用户界面管理器
pub struct CliUI {
    input_handler: InputHandler,
    display: CliDisplay,
}

impl CliUI {
    pub fn new() -> Self {
        Self {
            input_handler: InputHandler::new(),
            display: CliDisplay::new(),
        }
    }



    /// 运行 Framework 交互模式
    pub async fn run_framework_interactive_mode(&mut self, agent: Arc<CodeAssistantAgent>) -> Result<(), Box<dyn std::error::Error>> {
        // 显示欢迎信息
        self.display.print_welcome();
        println!("{}", style("🚀 Using framework-based agent system").green());
        println!("Available tools: file_edit, directory_ops, code_search, code_analysis");
        println!("Type 'help' for commands, 'exit' to quit.\n");

        // 显示输入提示
        self.input_handler.show_input_help();

        loop {
            // 获取用户输入
            match self.input_handler.get_input("👤 ") {
                Ok(input) => {
                    if input.is_empty() {
                        continue;
                    }

                    // 处理内置命令
                    if input == "exit" || input == "quit" {
                        println!("👋 {}", style("再见！感谢使用 Minimal Agent").cyan());
                        break;
                    }

                    if input == "help" {
                        self.show_framework_help();
                        continue;
                    }

                    if input == "clear" {
                        self.display.clear_screen();
                        self.display.print_welcome();
                        continue;
                    }

                    // 发送给 Framework Agent 处理
                    self.process_framework_request(agent.clone(), &input).await;
                }
                Err(e) => {
                    self.display.print_error(&format!("输入错误: {}", e));
                }
            }
        }

        // 保存输入历史记录
        if let Err(e) = self.input_handler.save_history() {
            tracing::warn!("Failed to save input history: {}", e);
        }

        Ok(())
    }

    /// 运行 Framework 单次查询模式
    pub async fn run_framework_single_query_mode(&self, agent: Arc<CodeAssistantAgent>) -> Result<(), Box<dyn std::error::Error>> {
        println!("🚀 Framework-based Code Assistant - 单次查询模式");
        print!("请输入您的查询: ");
        std::io::Write::flush(&mut std::io::stdout())?;

        let mut input = String::new();
        std::io::stdin().read_line(&mut input)?;
        let input = input.trim();

        if input.is_empty() {
            println!("未提供输入。");
            return Ok(());
        }

        self.process_framework_request(agent, input).await;

        Ok(())
    }

    /// 处理 Framework Agent 请求
    async fn process_framework_request(&self, agent: Arc<CodeAssistantAgent>, input: &str) {
        println!("\n🤖 Processing your request...\n");

        // 运行任务
        let mut task = agent.run_task(input.to_string()).await;
        let mut stream = task.stream();

        // 处理事件流
        while let Some(event) = stream.next().await {
            match event {
                Event::ResponseGenerated { agent_name, response_text } => {
                    println!("🧠 {}: {}", agent_name, response_text);
                }
                Event::ProcessingComplete { output } => {
                    println!("\n✅ Task completed!");
                    println!("{:?}", output.payload);
                    break;
                }
                Event::Interrupt(interrupt) => {
                    println!("⚠️  Task interrupted: {:?}", interrupt);
                    break;
                }
                _ => {
                    // 处理其他事件类型
                    tracing::debug!("Received event: {:?}", event);
                }
            }
        }

        println!("\n{}\n", "─".repeat(50));
    }

    /// 显示 Framework 帮助信息
    fn show_framework_help(&self) {
        println!("\n📚 Framework-based Code Assistant Help");
        println!("═══════════════════════════════════════");
        println!("Available commands:");
        println!("  help     - Show this help message");
        println!("  exit     - Exit the application");
        println!("  quit     - Exit the application");
        println!();
        println!("Available tools:");
        println!("  📝 file_edit     - Create, read, update, delete files");
        println!("  📁 directory_ops - List, create, delete directories");
        println!("  🔍 code_search   - Search for patterns in code");
        println!("  📊 code_analysis - Analyze code statistics and quality");
        println!();
        println!("Examples:");
        println!("  'Create a new Rust file called hello.rs with a main function'");
        println!("  'List all files in the current directory'");
        println!("  'Search for all TODO comments in the project'");
        println!("  'Analyze the code quality of src/ directory'");
        println!("═══════════════════════════════════════\n");
    }

    /// 创建进度条
    fn create_progress_bar(&self, message: &str) -> ProgressBar {
        let pb = ProgressBar::new_spinner();
        pb.set_style(
            ProgressStyle::default_spinner()
                .tick_strings(&["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"])
                .template("{spinner:.green} {msg}")
                .unwrap(),
        );
        pb.set_message(message.to_string());
        pb
    }


}

impl Default for CliUI {
    fn default() -> Self {
        Self::new()
    }
}
