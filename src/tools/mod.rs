pub mod executor;
pub mod registry;
pub mod builtin;
pub mod framework_tools;

pub use executor::ToolExecutor;
pub use registry::{ToolRegistry, Tool, ToolDefinition};
pub use framework_tools::{SimpleEchoTool, SimpleFileReadTool, SimpleFileWriteTool, SimpleListDirTool};

use crate::types::ToolExecutionResult;
use crate::Result;
use async_trait::async_trait;
use serde_json::Value;

/// 工具特征，所有工具都需要实现这个特征
#[async_trait]
pub trait ToolTrait: Send + Sync {
    /// 工具名称
    fn name(&self) -> &str;

    /// 工具描述
    fn description(&self) -> &str;

    /// 工具参数 JSON Schema
    fn parameters_schema(&self) -> Value;

    /// 执行工具
    async fn execute(&self, parameters: Value) -> Result<ToolExecutionResult>;
}
