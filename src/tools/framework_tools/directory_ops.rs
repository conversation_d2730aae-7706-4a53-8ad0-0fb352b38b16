use async_trait::async_trait;
use framework::core::{
    error::ToolError,
    tool::{Tool, ToolResult},
};
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use std::borrow::Cow;
use std::path::Path;
use tokio::fs;

/// 目录操作工具
pub struct DirectoryOpsTool;

/// 目录操作参数
#[derive(Debug, Clone, Deserialize, JsonSchema)]
pub struct DirectoryOpsParams {
    /// 操作类型
    pub operation: DirectoryOperation,
    /// 目录路径
    pub directory_path: String,
    /// 递归操作（列出目录时可选）
    pub recursive: Option<bool>,
    /// 包含隐藏文件（列出目录时可选）
    pub include_hidden: Option<bool>,
    /// 文件类型过滤（列出目录时可选）
    pub file_types: Option<Vec<String>>,
}

/// 目录操作类型
#[derive(Debug, C<PERSON>, Deserialize, JsonSchema)]
#[serde(rename_all = "snake_case")]
pub enum DirectoryOperation {
    /// 列出目录内容
    List,
    /// 创建目录
    Create,
    /// 删除目录
    Delete,
    /// 检查目录是否存在
    Exists,
    /// 获取目录信息
    Info,
}

/// 目录操作结果
#[derive(Debug, Clone, Serialize)]
pub struct DirectoryOpsResult {
    /// 操作是否成功
    pub success: bool,
    /// 目录路径
    pub directory_path: String,
    /// 操作类型
    pub operation: String,
    /// 目录内容（列出时返回）
    pub contents: Option<Vec<FileInfo>>,
    /// 目录是否存在
    pub exists: Option<bool>,
    /// 目录信息
    pub info: Option<DirectoryInfo>,
    /// 消息
    pub message: String,
}

/// 文件信息
#[derive(Debug, Clone, Serialize)]
pub struct FileInfo {
    /// 文件名
    pub name: String,
    /// 文件路径
    pub path: String,
    /// 是否为目录
    pub is_directory: bool,
    /// 文件大小（字节）
    pub size: Option<u64>,
    /// 文件扩展名
    pub extension: Option<String>,
    /// 修改时间
    pub modified: Option<String>,
}

/// 目录信息
#[derive(Debug, Clone, Serialize)]
pub struct DirectoryInfo {
    /// 目录路径
    pub path: String,
    /// 文件数量
    pub file_count: usize,
    /// 子目录数量
    pub directory_count: usize,
    /// 总大小（字节）
    pub total_size: u64,
}

#[async_trait]
impl Tool for DirectoryOpsTool {
    type Params = DirectoryOpsParams;
    type Result = DirectoryOpsResult;

    fn name(&self) -> Cow<str> {
        "directory_ops".into()
    }

    fn description(&self) -> Cow<str> {
        "Perform directory operations: list contents, create, delete, check existence, and get directory information.".into()
    }

    async fn call(&self, param: Option<Self::Params>) -> ToolResult<Self::Result> {
        let params = param.ok_or_else(|| ToolError::ValidateError("Missing parameters".to_string()))?;

        let dir_path = Path::new(&params.directory_path);
        
        // 验证目录路径
        if params.directory_path.is_empty() {
            return Err(ToolError::ValidateError("Directory path cannot be empty".to_string()));
        }

        match params.operation {
            DirectoryOperation::List => self.list_directory(dir_path, &params).await,
            DirectoryOperation::Create => self.create_directory(dir_path).await,
            DirectoryOperation::Delete => self.delete_directory(dir_path).await,
            DirectoryOperation::Exists => self.check_exists(dir_path).await,
            DirectoryOperation::Info => self.get_directory_info(dir_path, &params).await,
        }
    }
}

impl DirectoryOpsTool {
    /// 列出目录内容
    async fn list_directory(&self, dir_path: &Path, params: &DirectoryOpsParams) -> ToolResult<DirectoryOpsResult> {
        if !dir_path.exists() {
            return Err(ToolError::ExecutionError(format!("Directory not found: {}", dir_path.display())));
        }

        if !dir_path.is_dir() {
            return Err(ToolError::ExecutionError(format!("Path is not a directory: {}", dir_path.display())));
        }

        let mut contents = Vec::new();
        let recursive = params.recursive.unwrap_or(false);
        let include_hidden = params.include_hidden.unwrap_or(false);

        if recursive {
            self.list_recursive(dir_path, &mut contents, include_hidden, &params.file_types).await?;
        } else {
            self.list_single_level(dir_path, &mut contents, include_hidden, &params.file_types).await?;
        }

        Ok(DirectoryOpsResult {
            success: true,
            directory_path: dir_path.to_string_lossy().to_string(),
            operation: "list".to_string(),
            contents: Some(contents),
            exists: None,
            info: None,
            message: format!("Listed {} items in directory: {}", contents.len(), dir_path.display()),
        })
    }

    /// 列出单层目录内容
    async fn list_single_level(
        &self,
        dir_path: &Path,
        contents: &mut Vec<FileInfo>,
        include_hidden: bool,
        file_types: &Option<Vec<String>>,
    ) -> ToolResult<()> {
        let mut entries = fs::read_dir(dir_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read directory: {}", e)))?;

        while let Some(entry) = entries.next_entry().await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read directory entry: {}", e)))? {
            
            let path = entry.path();
            let file_name = path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("")
                .to_string();

            // 跳过隐藏文件（如果不包含）
            if !include_hidden && file_name.starts_with('.') {
                continue;
            }

            let metadata = entry.metadata().await
                .map_err(|e| ToolError::ExecutionError(format!("Failed to read metadata: {}", e)))?;

            let is_directory = metadata.is_dir();
            let size = if is_directory { None } else { Some(metadata.len()) };
            
            let extension = if is_directory {
                None
            } else {
                path.extension().and_then(|ext| ext.to_str()).map(|s| s.to_string())
            };

            // 文件类型过滤
            if let Some(types) = file_types {
                if !is_directory {
                    if let Some(ext) = &extension {
                        if !types.contains(ext) {
                            continue;
                        }
                    } else if !types.is_empty() {
                        continue;
                    }
                }
            }

            let modified = metadata.modified()
                .ok()
                .and_then(|time| time.duration_since(std::time::UNIX_EPOCH).ok())
                .map(|duration| {
                    chrono::DateTime::from_timestamp(duration.as_secs() as i64, 0)
                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
                        .unwrap_or_default()
                });

            contents.push(FileInfo {
                name: file_name,
                path: path.to_string_lossy().to_string(),
                is_directory,
                size,
                extension,
                modified,
            });
        }

        Ok(())
    }

    /// 递归列出目录内容
    async fn list_recursive(
        &self,
        dir_path: &Path,
        contents: &mut Vec<FileInfo>,
        include_hidden: bool,
        file_types: &Option<Vec<String>>,
    ) -> ToolResult<()> {
        self.list_single_level(dir_path, contents, include_hidden, file_types).await?;

        let mut entries = fs::read_dir(dir_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read directory: {}", e)))?;

        while let Some(entry) = entries.next_entry().await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read directory entry: {}", e)))? {
            
            let path = entry.path();
            if path.is_dir() {
                let file_name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("");

                // 跳过隐藏目录（如果不包含）
                if !include_hidden && file_name.starts_with('.') {
                    continue;
                }

                self.list_recursive(&path, contents, include_hidden, file_types).await?;
            }
        }

        Ok(())
    }

    /// 创建目录
    async fn create_directory(&self, dir_path: &Path) -> ToolResult<DirectoryOpsResult> {
        if dir_path.exists() {
            return Err(ToolError::ExecutionError(format!("Directory already exists: {}", dir_path.display())));
        }

        fs::create_dir_all(dir_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to create directory: {}", e)))?;

        Ok(DirectoryOpsResult {
            success: true,
            directory_path: dir_path.to_string_lossy().to_string(),
            operation: "create".to_string(),
            contents: None,
            exists: Some(true),
            info: None,
            message: format!("Directory created successfully: {}", dir_path.display()),
        })
    }

    /// 删除目录
    async fn delete_directory(&self, dir_path: &Path) -> ToolResult<DirectoryOpsResult> {
        if !dir_path.exists() {
            return Err(ToolError::ExecutionError(format!("Directory not found: {}", dir_path.display())));
        }

        if !dir_path.is_dir() {
            return Err(ToolError::ExecutionError(format!("Path is not a directory: {}", dir_path.display())));
        }

        fs::remove_dir_all(dir_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to delete directory: {}", e)))?;

        Ok(DirectoryOpsResult {
            success: true,
            directory_path: dir_path.to_string_lossy().to_string(),
            operation: "delete".to_string(),
            contents: None,
            exists: Some(false),
            info: None,
            message: format!("Directory deleted successfully: {}", dir_path.display()),
        })
    }

    /// 检查目录是否存在
    async fn check_exists(&self, dir_path: &Path) -> ToolResult<DirectoryOpsResult> {
        let exists = dir_path.exists() && dir_path.is_dir();

        Ok(DirectoryOpsResult {
            success: true,
            directory_path: dir_path.to_string_lossy().to_string(),
            operation: "exists".to_string(),
            contents: None,
            exists: Some(exists),
            info: None,
            message: format!("Directory {} {}", 
                           dir_path.display(), 
                           if exists { "exists" } else { "does not exist" }),
        })
    }

    /// 获取目录信息
    async fn get_directory_info(&self, dir_path: &Path, params: &DirectoryOpsParams) -> ToolResult<DirectoryOpsResult> {
        if !dir_path.exists() {
            return Err(ToolError::ExecutionError(format!("Directory not found: {}", dir_path.display())));
        }

        if !dir_path.is_dir() {
            return Err(ToolError::ExecutionError(format!("Path is not a directory: {}", dir_path.display())));
        }

        let mut file_count = 0;
        let mut directory_count = 0;
        let mut total_size = 0;

        let recursive = params.recursive.unwrap_or(false);
        self.calculate_directory_stats(dir_path, &mut file_count, &mut directory_count, &mut total_size, recursive).await?;

        let info = DirectoryInfo {
            path: dir_path.to_string_lossy().to_string(),
            file_count,
            directory_count,
            total_size,
        };

        Ok(DirectoryOpsResult {
            success: true,
            directory_path: dir_path.to_string_lossy().to_string(),
            operation: "info".to_string(),
            contents: None,
            exists: Some(true),
            info: Some(info),
            message: format!("Directory info: {} files, {} directories, {} bytes total", 
                           file_count, directory_count, total_size),
        })
    }

    /// 计算目录统计信息
    async fn calculate_directory_stats(
        &self,
        dir_path: &Path,
        file_count: &mut usize,
        directory_count: &mut usize,
        total_size: &mut u64,
        recursive: bool,
    ) -> ToolResult<()> {
        let mut entries = fs::read_dir(dir_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read directory: {}", e)))?;

        while let Some(entry) = entries.next_entry().await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read directory entry: {}", e)))? {
            
            let path = entry.path();
            let metadata = entry.metadata().await
                .map_err(|e| ToolError::ExecutionError(format!("Failed to read metadata: {}", e)))?;

            if metadata.is_dir() {
                *directory_count += 1;
                if recursive {
                    self.calculate_directory_stats(&path, file_count, directory_count, total_size, recursive).await?;
                }
            } else {
                *file_count += 1;
                *total_size += metadata.len();
            }
        }

        Ok(())
    }
}
