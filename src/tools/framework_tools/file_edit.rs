use async_trait::async_trait;
use framework::core::{
    error::ToolError,
    tool::{Tool, ToolResult},
};
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use std::borrow::Cow;
use std::path::Path;
use tokio::fs;

/// 文件编辑工具
pub struct FileEditTool;

/// 文件编辑参数
#[derive(Debug, Clone, Deserialize, JsonSchema)]
pub struct FileEditParams {
    /// 操作类型
    pub operation: FileOperation,
    /// 文件路径
    pub file_path: String,
    /// 文件内容（创建和修改时需要）
    pub content: Option<String>,
    /// 备份原文件（修改时可选）
    pub backup: Option<bool>,
}

/// 文件操作类型
#[derive(Debug, Clone, Deserialize, JsonSchema)]
#[serde(rename_all = "snake_case")]
pub enum FileOperation {
    /// 创建文件
    Create,
    /// 读取文件
    Read,
    /// 修改文件
    Update,
    /// 删除文件
    Delete,
    /// 追加内容
    Append,
}

/// 文件编辑结果
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize)]
pub struct FileEditResult {
    /// 操作是否成功
    pub success: bool,
    /// 文件路径
    pub file_path: String,
    /// 操作类型
    pub operation: String,
    /// 文件内容（读取时返回）
    pub content: Option<String>,
    /// 文件大小
    pub file_size: Option<u64>,
    /// 消息
    pub message: String,
}

#[async_trait]
impl Tool for FileEditTool {
    type Params = FileEditParams;
    type Result = FileEditResult;

    fn name(&self) -> Cow<str> {
        "file_edit".into()
    }

    fn description(&self) -> Cow<str> {
        "Create, read, update, delete, or append to files. Supports various file operations for code editing tasks.".into()
    }

    async fn call(&self, param: Option<Self::Params>) -> ToolResult<Self::Result> {
        let params = param.ok_or_else(|| ToolError::ValidateError("Missing parameters".to_string()))?;

        let file_path = Path::new(&params.file_path);
        
        // 验证文件路径
        if params.file_path.is_empty() {
            return Err(ToolError::ValidateError("File path cannot be empty".to_string()));
        }

        match params.operation {
            FileOperation::Create => self.create_file(file_path, &params).await,
            FileOperation::Read => self.read_file(file_path).await,
            FileOperation::Update => self.update_file(file_path, &params).await,
            FileOperation::Delete => self.delete_file(file_path).await,
            FileOperation::Append => self.append_file(file_path, &params).await,
        }
    }
}

impl FileEditTool {
    /// 创建文件
    async fn create_file(&self, file_path: &Path, params: &FileEditParams) -> ToolResult<FileEditResult> {
        let content = params.content.as_deref().unwrap_or("");

        // 创建父目录
        if let Some(parent) = file_path.parent() {
            fs::create_dir_all(parent).await
                .map_err(|e| ToolError::ExecuteError {
                    tool_name: "file_edit".to_string(),
                    message: format!("Failed to create directory: {}", e)
                })?;
        }

        // 检查文件是否已存在
        if file_path.exists() {
            return Err(ToolError::ExecuteError {
                tool_name: "file_edit".to_string(),
                message: format!("File already exists: {}", file_path.display())
            });
        }

        // 创建文件
        fs::write(file_path, content).await
            .map_err(|e| ToolError::ExecuteError {
                tool_name: "file_edit".to_string(),
                message: format!("Failed to create file: {}", e)
            })?;

        let file_size = fs::metadata(file_path).await
            .map(|m| m.len())
            .unwrap_or(0);

        Ok(FileEditResult {
            success: true,
            file_path: file_path.to_string_lossy().to_string(),
            operation: "create".to_string(),
            content: Some(content.to_string()),
            file_size: Some(file_size),
            message: format!("File created successfully: {}", file_path.display()),
        })
    }

    /// 读取文件
    async fn read_file(&self, file_path: &Path) -> ToolResult<FileEditResult> {
        if !file_path.exists() {
            return Err(ToolError::ExecutionError(format!("File not found: {}", file_path.display())));
        }

        let content = fs::read_to_string(file_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read file: {}", e)))?;

        let file_size = fs::metadata(file_path).await
            .map(|m| m.len())
            .unwrap_or(0);

        Ok(FileEditResult {
            success: true,
            file_path: file_path.to_string_lossy().to_string(),
            operation: "read".to_string(),
            content: Some(content),
            file_size: Some(file_size),
            message: format!("File read successfully: {}", file_path.display()),
        })
    }

    /// 更新文件
    async fn update_file(&self, file_path: &Path, params: &FileEditParams) -> ToolResult<FileEditResult> {
        let content = params.content.as_deref().unwrap_or("");

        if !file_path.exists() {
            return Err(ToolError::ExecutionError(format!("File not found: {}", file_path.display())));
        }

        // 备份原文件（如果需要）
        if params.backup.unwrap_or(false) {
            let backup_path = format!("{}.backup", file_path.to_string_lossy());
            fs::copy(file_path, &backup_path).await
                .map_err(|e| ToolError::ExecutionError(format!("Failed to create backup: {}", e)))?;
        }

        // 更新文件
        fs::write(file_path, content).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to update file: {}", e)))?;

        let file_size = fs::metadata(file_path).await
            .map(|m| m.len())
            .unwrap_or(0);

        Ok(FileEditResult {
            success: true,
            file_path: file_path.to_string_lossy().to_string(),
            operation: "update".to_string(),
            content: Some(content.to_string()),
            file_size: Some(file_size),
            message: format!("File updated successfully: {}", file_path.display()),
        })
    }

    /// 删除文件
    async fn delete_file(&self, file_path: &Path) -> ToolResult<FileEditResult> {
        if !file_path.exists() {
            return Err(ToolError::ExecutionError(format!("File not found: {}", file_path.display())));
        }

        fs::remove_file(file_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to delete file: {}", e)))?;

        Ok(FileEditResult {
            success: true,
            file_path: file_path.to_string_lossy().to_string(),
            operation: "delete".to_string(),
            content: None,
            file_size: None,
            message: format!("File deleted successfully: {}", file_path.display()),
        })
    }

    /// 追加内容到文件
    async fn append_file(&self, file_path: &Path, params: &FileEditParams) -> ToolResult<FileEditResult> {
        let content = params.content.as_deref().unwrap_or("");

        if !file_path.exists() {
            return Err(ToolError::ExecutionError(format!("File not found: {}", file_path.display())));
        }

        // 读取现有内容
        let mut existing_content = fs::read_to_string(file_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read file: {}", e)))?;

        // 追加新内容
        existing_content.push_str(content);

        // 写回文件
        fs::write(file_path, &existing_content).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to append to file: {}", e)))?;

        let file_size = fs::metadata(file_path).await
            .map(|m| m.len())
            .unwrap_or(0);

        Ok(FileEditResult {
            success: true,
            file_path: file_path.to_string_lossy().to_string(),
            operation: "append".to_string(),
            content: Some(existing_content),
            file_size: Some(file_size),
            message: format!("Content appended successfully to: {}", file_path.display()),
        })
    }
}
