use async_trait::async_trait;
use framework::core::{
    error::ToolError,
    tool::{Tool, ToolResult},
};
use serde::{Deserialize, Serialize};
use schemars::JsonSchema;
use std::borrow::Cow;

/// 简单的回声工具
pub struct SimpleEchoTool;

/// 回声工具参数
#[derive(Debug, Clone, Deserialize, JsonSchema)]
pub struct EchoParams {
    /// 要回声的消息
    pub message: String,
}

/// 回声工具结果
#[derive(Debug, Clone, Serialize)]
pub struct EchoResult {
    /// 回声的消息
    pub echoed_message: String,
}

#[async_trait]
impl Tool for SimpleEchoTool {
    type Params = EchoParams;
    type Result = EchoResult;

    fn name(&self) -> Cow<str> {
        "echo".into()
    }

    fn description(&self) -> Cow<str> {
        "Echo back the provided message".into()
    }

    async fn call(&self, param: Option<Self::Params>) -> ToolResult<Self::Result> {
        let params = param.ok_or_else(|| ToolError::ValidateError("Missing parameters".to_string()))?;

        Ok(EchoResult {
            echoed_message: params.message,
        })
    }
}

/// 简单的文件读取工具
pub struct SimpleFileReadTool;

/// 文件读取参数
#[derive(Debug, Clone, Deserialize, JsonSchema)]
pub struct FileReadParams {
    /// 文件路径
    pub file_path: String,
}

/// 文件读取结果
#[derive(Debug, Clone, Serialize)]
pub struct FileReadResult {
    /// 文件内容
    pub content: String,
    /// 文件大小
    pub size: usize,
}

#[async_trait]
impl Tool for SimpleFileReadTool {
    type Params = FileReadParams;
    type Result = FileReadResult;

    fn name(&self) -> Cow<str> {
        "file_read".into()
    }

    fn description(&self) -> Cow<str> {
        "Read the contents of a file".into()
    }

    async fn call(&self, param: Option<Self::Params>) -> ToolResult<Self::Result> {
        let params = param.ok_or_else(|| ToolError::ValidateError("Missing parameters".to_string()))?;

        let content = tokio::fs::read_to_string(&params.file_path).await
            .map_err(|e| ToolError::ExecuteError {
                tool_name: "file_read".to_string(),
                message: format!("Failed to read file: {}", e),
            })?;

        Ok(FileReadResult {
            size: content.len(),
            content,
        })
    }
}

/// 简单的文件写入工具
pub struct SimpleFileWriteTool;

/// 文件写入参数
#[derive(Debug, Clone, Deserialize, JsonSchema)]
pub struct FileWriteParams {
    /// 文件路径
    pub file_path: String,
    /// 文件内容
    pub content: String,
}

/// 文件写入结果
#[derive(Debug, Clone, Serialize)]
pub struct FileWriteResult {
    /// 写入的字节数
    pub bytes_written: usize,
    /// 成功消息
    pub message: String,
}

#[async_trait]
impl Tool for SimpleFileWriteTool {
    type Params = FileWriteParams;
    type Result = FileWriteResult;

    fn name(&self) -> Cow<str> {
        "file_write".into()
    }

    fn description(&self) -> Cow<str> {
        "Write content to a file".into()
    }

    async fn call(&self, param: Option<Self::Params>) -> ToolResult<Self::Result> {
        let params = param.ok_or_else(|| ToolError::ValidateError("Missing parameters".to_string()))?;

        // 创建父目录
        if let Some(parent) = std::path::Path::new(&params.file_path).parent() {
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| ToolError::ExecuteError {
                    tool_name: "file_write".to_string(),
                    message: format!("Failed to create directory: {}", e),
                })?;
        }

        tokio::fs::write(&params.file_path, &params.content).await
            .map_err(|e| ToolError::ExecuteError {
                tool_name: "file_write".to_string(),
                message: format!("Failed to write file: {}", e),
            })?;

        Ok(FileWriteResult {
            bytes_written: params.content.len(),
            message: format!("Successfully wrote {} bytes to {}", params.content.len(), params.file_path),
        })
    }
}

/// 简单的目录列表工具
pub struct SimpleListDirTool;

/// 目录列表参数
#[derive(Debug, Clone, Deserialize, JsonSchema)]
pub struct ListDirParams {
    /// 目录路径
    pub directory_path: String,
}

/// 目录列表结果
#[derive(Debug, Clone, Serialize)]
pub struct ListDirResult {
    /// 文件和目录列表
    pub entries: Vec<String>,
    /// 条目数量
    pub count: usize,
}

#[async_trait]
impl Tool for SimpleListDirTool {
    type Params = ListDirParams;
    type Result = ListDirResult;

    fn name(&self) -> Cow<str> {
        "list_directory".into()
    }

    fn description(&self) -> Cow<str> {
        "List the contents of a directory".into()
    }

    async fn call(&self, param: Option<Self::Params>) -> ToolResult<Self::Result> {
        let params = param.ok_or_else(|| ToolError::ValidateError("Missing parameters".to_string()))?;

        let mut entries = Vec::new();
        let mut dir_entries = tokio::fs::read_dir(&params.directory_path).await
            .map_err(|e| ToolError::ExecuteError {
                tool_name: "list_directory".to_string(),
                message: format!("Failed to read directory: {}", e),
            })?;

        while let Some(entry) = dir_entries.next_entry().await
            .map_err(|e| ToolError::ExecuteError {
                tool_name: "list_directory".to_string(),
                message: format!("Failed to read directory entry: {}", e),
            })? {
            
            if let Some(name) = entry.file_name().to_str() {
                entries.push(name.to_string());
            }
        }

        entries.sort();
        let count = entries.len();

        Ok(ListDirResult {
            entries,
            count,
        })
    }
}
