use async_trait::async_trait;
use framework::core::{
    error::ToolError,
    tool::{Tool, ToolResult},
};
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use std::borrow::Cow;
use std::path::Path;
use tokio::fs;
use regex::Regex;

/// 代码搜索工具
pub struct CodeSearchTool;

/// 代码搜索参数
#[derive(Debug, Clone, Deserialize, JsonSchema)]
pub struct CodeSearchParams {
    /// 搜索模式
    pub pattern: String,
    /// 搜索路径
    pub search_path: String,
    /// 是否使用正则表达式
    pub use_regex: Option<bool>,
    /// 是否区分大小写
    pub case_sensitive: Option<bool>,
    /// 文件类型过滤
    pub file_extensions: Option<Vec<String>>,
    /// 是否递归搜索
    pub recursive: Option<bool>,
    /// 最大结果数量
    pub max_results: Option<usize>,
    /// 上下文行数
    pub context_lines: Option<usize>,
}

/// 代码搜索结果
#[derive(Debug, <PERSON><PERSON>, Serialize)]
pub struct CodeSearchResult {
    /// 搜索是否成功
    pub success: bool,
    /// 搜索模式
    pub pattern: String,
    /// 搜索路径
    pub search_path: String,
    /// 匹配结果
    pub matches: Vec<SearchMatch>,
    /// 总匹配数
    pub total_matches: usize,
    /// 搜索的文件数
    pub files_searched: usize,
    /// 消息
    pub message: String,
}

/// 搜索匹配结果
#[derive(Debug, Clone, Serialize)]
pub struct SearchMatch {
    /// 文件路径
    pub file_path: String,
    /// 行号
    pub line_number: usize,
    /// 匹配的行内容
    pub line_content: String,
    /// 上下文行（如果请求）
    pub context_before: Option<Vec<String>>,
    /// 上下文行（如果请求）
    pub context_after: Option<Vec<String>>,
    /// 匹配的列位置
    pub column_start: Option<usize>,
    /// 匹配的列结束位置
    pub column_end: Option<usize>,
}

#[async_trait]
impl Tool for CodeSearchTool {
    type Params = CodeSearchParams;
    type Result = CodeSearchResult;

    fn name(&self) -> Cow<str> {
        "code_search".into()
    }

    fn description(&self) -> Cow<str> {
        "Search for patterns in code files. Supports regex, case sensitivity, file type filtering, and context lines.".into()
    }

    async fn call(&self, param: Option<Self::Params>) -> ToolResult<Self::Result> {
        let params = param.ok_or_else(|| ToolError::ValidateError("Missing parameters".to_string()))?;

        if params.pattern.is_empty() {
            return Err(ToolError::ValidateError("Search pattern cannot be empty".to_string()));
        }

        let search_path = Path::new(&params.search_path);
        if !search_path.exists() {
            return Err(ToolError::ExecutionError(format!("Search path not found: {}", search_path.display())));
        }

        let use_regex = params.use_regex.unwrap_or(false);
        let case_sensitive = params.case_sensitive.unwrap_or(true);
        let recursive = params.recursive.unwrap_or(true);
        let max_results = params.max_results.unwrap_or(100);
        let context_lines = params.context_lines.unwrap_or(0);

        // 编译正则表达式（如果使用）
        let regex = if use_regex {
            let pattern = if case_sensitive {
                params.pattern.clone()
            } else {
                format!("(?i){}", params.pattern)
            };
            
            Some(Regex::new(&pattern)
                .map_err(|e| ToolError::ValidateError(format!("Invalid regex pattern: {}", e)))?)
        } else {
            None
        };

        let mut matches = Vec::new();
        let mut files_searched = 0;

        if search_path.is_file() {
            if self.should_search_file(search_path, &params.file_extensions) {
                self.search_in_file(
                    search_path,
                    &params.pattern,
                    &regex,
                    case_sensitive,
                    context_lines,
                    &mut matches,
                    max_results,
                ).await?;
                files_searched = 1;
            }
        } else {
            self.search_in_directory(
                search_path,
                &params.pattern,
                &regex,
                case_sensitive,
                recursive,
                &params.file_extensions,
                context_lines,
                &mut matches,
                &mut files_searched,
                max_results,
            ).await?;
        }

        let total_matches = matches.len();

        Ok(CodeSearchResult {
            success: true,
            pattern: params.pattern,
            search_path: params.search_path,
            matches,
            total_matches,
            files_searched,
            message: format!("Found {} matches in {} files", total_matches, files_searched),
        })
    }
}

impl CodeSearchTool {
    /// 检查是否应该搜索该文件
    fn should_search_file(&self, file_path: &Path, file_extensions: &Option<Vec<String>>) -> bool {
        if let Some(extensions) = file_extensions {
            if let Some(ext) = file_path.extension().and_then(|e| e.to_str()) {
                extensions.contains(&ext.to_string())
            } else {
                false
            }
        } else {
            // 默认搜索常见的代码文件
            if let Some(ext) = file_path.extension().and_then(|e| e.to_str()) {
                matches!(ext, "rs" | "py" | "js" | "ts" | "java" | "cpp" | "c" | "h" | "go" | "rb" | "php" | "cs" | "swift" | "kt" | "scala" | "clj" | "hs" | "ml" | "fs" | "elm" | "dart" | "r" | "jl" | "lua" | "pl" | "sh" | "bash" | "zsh" | "fish" | "ps1" | "bat" | "cmd" | "html" | "css" | "scss" | "sass" | "less" | "xml" | "json" | "yaml" | "yml" | "toml" | "ini" | "cfg" | "conf" | "md" | "txt" | "log")
            } else {
                false
            }
        }
    }

    /// 在目录中搜索
    async fn search_in_directory(
        &self,
        dir_path: &Path,
        pattern: &str,
        regex: &Option<Regex>,
        case_sensitive: bool,
        recursive: bool,
        file_extensions: &Option<Vec<String>>,
        context_lines: usize,
        matches: &mut Vec<SearchMatch>,
        files_searched: &mut usize,
        max_results: usize,
    ) -> ToolResult<()> {
        if matches.len() >= max_results {
            return Ok(());
        }

        let mut entries = fs::read_dir(dir_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read directory: {}", e)))?;

        while let Some(entry) = entries.next_entry().await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read directory entry: {}", e)))? {
            
            if matches.len() >= max_results {
                break;
            }

            let path = entry.path();
            
            if path.is_file() {
                if self.should_search_file(&path, file_extensions) {
                    self.search_in_file(
                        &path,
                        pattern,
                        regex,
                        case_sensitive,
                        context_lines,
                        matches,
                        max_results,
                    ).await?;
                    *files_searched += 1;
                }
            } else if path.is_dir() && recursive {
                // 跳过隐藏目录和常见的忽略目录
                if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                    if !dir_name.starts_with('.') && !matches!(dir_name, "node_modules" | "target" | "build" | "dist" | "__pycache__" | ".git" | ".svn" | ".hg") {
                        self.search_in_directory(
                            &path,
                            pattern,
                            regex,
                            case_sensitive,
                            recursive,
                            file_extensions,
                            context_lines,
                            matches,
                            files_searched,
                            max_results,
                        ).await?;
                    }
                }
            }
        }

        Ok(())
    }

    /// 在文件中搜索
    async fn search_in_file(
        &self,
        file_path: &Path,
        pattern: &str,
        regex: &Option<Regex>,
        case_sensitive: bool,
        context_lines: usize,
        matches: &mut Vec<SearchMatch>,
        max_results: usize,
    ) -> ToolResult<()> {
        if matches.len() >= max_results {
            return Ok(());
        }

        let content = match fs::read_to_string(file_path).await {
            Ok(content) => content,
            Err(_) => return Ok(()), // 跳过无法读取的文件（可能是二进制文件）
        };

        let lines: Vec<&str> = content.lines().collect();
        
        for (line_number, line) in lines.iter().enumerate() {
            if matches.len() >= max_results {
                break;
            }

            let (is_match, column_start, column_end) = if let Some(regex) = regex {
                if let Some(mat) = regex.find(line) {
                    (true, Some(mat.start()), Some(mat.end()))
                } else {
                    (false, None, None)
                }
            } else {
                let search_line = if case_sensitive { line } else { &line.to_lowercase() };
                let search_pattern = if case_sensitive { pattern } else { &pattern.to_lowercase() };
                
                if let Some(pos) = search_line.find(search_pattern) {
                    (true, Some(pos), Some(pos + search_pattern.len()))
                } else {
                    (false, None, None)
                }
            };

            if is_match {
                let context_before = if context_lines > 0 {
                    let start = line_number.saturating_sub(context_lines);
                    Some(lines[start..line_number].iter().map(|s| s.to_string()).collect())
                } else {
                    None
                };

                let context_after = if context_lines > 0 {
                    let end = std::cmp::min(line_number + 1 + context_lines, lines.len());
                    Some(lines[line_number + 1..end].iter().map(|s| s.to_string()).collect())
                } else {
                    None
                };

                matches.push(SearchMatch {
                    file_path: file_path.to_string_lossy().to_string(),
                    line_number: line_number + 1, // 1-based line numbers
                    line_content: line.to_string(),
                    context_before,
                    context_after,
                    column_start,
                    column_end,
                });
            }
        }

        Ok(())
    }
}
