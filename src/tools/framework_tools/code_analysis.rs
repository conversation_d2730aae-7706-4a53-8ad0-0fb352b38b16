use async_trait::async_trait;
use framework::core::{
    error::ToolError,
    tool::{<PERSON><PERSON>, ToolR<PERSON>ult},
};
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use std::borrow::Cow;
use std::path::Path;
use tokio::fs;
use std::collections::HashMap;

/// 代码分析工具
pub struct CodeAnalysisTool;

/// 代码分析参数
#[derive(Debug, Clone, Deserialize, JsonSchema)]
pub struct CodeAnalysisParams {
    /// 分析类型
    pub analysis_type: AnalysisType,
    /// 文件或目录路径
    pub path: String,
    /// 是否递归分析（目录时）
    pub recursive: Option<bool>,
    /// 文件类型过滤
    pub file_extensions: Option<Vec<String>>,
    /// 分析选项
    pub options: Option<AnalysisOptions>,
}

/// 分析类型
#[derive(Debug, C<PERSON>, Deserialize, JsonSchema)]
#[serde(rename_all = "snake_case")]
pub enum AnalysisType {
    /// 代码统计
    Statistics,
    /// 代码复杂度
    Complexity,
    /// 代码结构
    Structure,
    /// 代码质量
    Quality,
    /// 依赖分析
    Dependencies,
}

/// 分析选项
#[derive(Debug, Clone, Deserialize, JsonSchema)]
pub struct AnalysisOptions {
    /// 包含注释统计
    pub include_comments: Option<bool>,
    /// 包含空行统计
    pub include_blank_lines: Option<bool>,
    /// 最大函数长度阈值
    pub max_function_length: Option<usize>,
    /// 最大复杂度阈值
    pub max_complexity: Option<usize>,
}

/// 代码分析结果
#[derive(Debug, Clone, Serialize)]
pub struct CodeAnalysisResult {
    /// 分析是否成功
    pub success: bool,
    /// 分析类型
    pub analysis_type: String,
    /// 分析路径
    pub path: String,
    /// 统计信息
    pub statistics: Option<CodeStatistics>,
    /// 复杂度信息
    pub complexity: Option<ComplexityAnalysis>,
    /// 结构信息
    pub structure: Option<StructureAnalysis>,
    /// 质量信息
    pub quality: Option<QualityAnalysis>,
    /// 依赖信息
    pub dependencies: Option<DependencyAnalysis>,
    /// 消息
    pub message: String,
}

/// 代码统计
#[derive(Debug, Clone, Serialize)]
pub struct CodeStatistics {
    /// 总文件数
    pub total_files: usize,
    /// 总行数
    pub total_lines: usize,
    /// 代码行数
    pub code_lines: usize,
    /// 注释行数
    pub comment_lines: usize,
    /// 空行数
    pub blank_lines: usize,
    /// 按语言分类的统计
    pub by_language: HashMap<String, LanguageStats>,
}

/// 语言统计
#[derive(Debug, Clone, Serialize)]
pub struct LanguageStats {
    /// 文件数
    pub files: usize,
    /// 行数
    pub lines: usize,
    /// 代码行数
    pub code_lines: usize,
    /// 注释行数
    pub comment_lines: usize,
}

/// 复杂度分析
#[derive(Debug, Clone, Serialize)]
pub struct ComplexityAnalysis {
    /// 平均复杂度
    pub average_complexity: f64,
    /// 最高复杂度
    pub max_complexity: usize,
    /// 复杂度分布
    pub complexity_distribution: HashMap<String, usize>,
    /// 高复杂度函数
    pub high_complexity_functions: Vec<ComplexFunction>,
}

/// 复杂函数
#[derive(Debug, Clone, Serialize)]
pub struct ComplexFunction {
    /// 文件路径
    pub file_path: String,
    /// 函数名
    pub function_name: String,
    /// 行号
    pub line_number: usize,
    /// 复杂度值
    pub complexity: usize,
}

/// 结构分析
#[derive(Debug, Clone, Serialize)]
pub struct StructureAnalysis {
    /// 模块数量
    pub modules: usize,
    /// 类数量
    pub classes: usize,
    /// 函数数量
    pub functions: usize,
    /// 平均函数长度
    pub average_function_length: f64,
    /// 最长函数
    pub longest_functions: Vec<LongFunction>,
}

/// 长函数
#[derive(Debug, Clone, Serialize)]
pub struct LongFunction {
    /// 文件路径
    pub file_path: String,
    /// 函数名
    pub function_name: String,
    /// 开始行号
    pub start_line: usize,
    /// 结束行号
    pub end_line: usize,
    /// 函数长度
    pub length: usize,
}

/// 质量分析
#[derive(Debug, Clone, Serialize)]
pub struct QualityAnalysis {
    /// 质量评分 (0-100)
    pub quality_score: f64,
    /// 问题列表
    pub issues: Vec<QualityIssue>,
    /// 建议
    pub suggestions: Vec<String>,
}

/// 质量问题
#[derive(Debug, Clone, Serialize)]
pub struct QualityIssue {
    /// 问题类型
    pub issue_type: String,
    /// 严重程度
    pub severity: String,
    /// 文件路径
    pub file_path: String,
    /// 行号
    pub line_number: Option<usize>,
    /// 问题描述
    pub description: String,
}

/// 依赖分析
#[derive(Debug, Clone, Serialize)]
pub struct DependencyAnalysis {
    /// 外部依赖
    pub external_dependencies: Vec<String>,
    /// 内部依赖
    pub internal_dependencies: HashMap<String, Vec<String>>,
    /// 循环依赖
    pub circular_dependencies: Vec<Vec<String>>,
}

#[async_trait]
impl Tool for CodeAnalysisTool {
    type Params = CodeAnalysisParams;
    type Result = CodeAnalysisResult;

    fn name(&self) -> Cow<str> {
        "code_analysis".into()
    }

    fn description(&self) -> Cow<str> {
        "Analyze code for statistics, complexity, structure, quality, and dependencies.".into()
    }

    async fn call(&self, param: Option<Self::Params>) -> ToolResult<Self::Result> {
        let params = param.ok_or_else(|| ToolError::ValidateError("Missing parameters".to_string()))?;

        let path = Path::new(&params.path);
        if !path.exists() {
            return Err(ToolError::ExecutionError(format!("Path not found: {}", path.display())));
        }

        let recursive = params.recursive.unwrap_or(true);

        match params.analysis_type {
            AnalysisType::Statistics => {
                let stats = self.analyze_statistics(path, recursive, &params.file_extensions, &params.options).await?;
                Ok(CodeAnalysisResult {
                    success: true,
                    analysis_type: "statistics".to_string(),
                    path: params.path,
                    statistics: Some(stats),
                    complexity: None,
                    structure: None,
                    quality: None,
                    dependencies: None,
                    message: "Code statistics analysis completed".to_string(),
                })
            }
            AnalysisType::Complexity => {
                let complexity = self.analyze_complexity(path, recursive, &params.file_extensions).await?;
                Ok(CodeAnalysisResult {
                    success: true,
                    analysis_type: "complexity".to_string(),
                    path: params.path,
                    statistics: None,
                    complexity: Some(complexity),
                    structure: None,
                    quality: None,
                    dependencies: None,
                    message: "Code complexity analysis completed".to_string(),
                })
            }
            AnalysisType::Structure => {
                let structure = self.analyze_structure(path, recursive, &params.file_extensions).await?;
                Ok(CodeAnalysisResult {
                    success: true,
                    analysis_type: "structure".to_string(),
                    path: params.path,
                    statistics: None,
                    complexity: None,
                    structure: Some(structure),
                    quality: None,
                    dependencies: None,
                    message: "Code structure analysis completed".to_string(),
                })
            }
            AnalysisType::Quality => {
                let quality = self.analyze_quality(path, recursive, &params.file_extensions, &params.options).await?;
                Ok(CodeAnalysisResult {
                    success: true,
                    analysis_type: "quality".to_string(),
                    path: params.path,
                    statistics: None,
                    complexity: None,
                    structure: None,
                    quality: Some(quality),
                    dependencies: None,
                    message: "Code quality analysis completed".to_string(),
                })
            }
            AnalysisType::Dependencies => {
                let dependencies = self.analyze_dependencies(path, recursive, &params.file_extensions).await?;
                Ok(CodeAnalysisResult {
                    success: true,
                    analysis_type: "dependencies".to_string(),
                    path: params.path,
                    statistics: None,
                    complexity: None,
                    structure: None,
                    quality: None,
                    dependencies: Some(dependencies),
                    message: "Dependency analysis completed".to_string(),
                })
            }
        }
    }
}

impl CodeAnalysisTool {
    /// 分析代码统计
    async fn analyze_statistics(
        &self,
        path: &Path,
        recursive: bool,
        file_extensions: &Option<Vec<String>>,
        options: &Option<AnalysisOptions>,
    ) -> ToolResult<CodeStatistics> {
        let include_comments = options.as_ref().and_then(|o| o.include_comments).unwrap_or(true);
        let include_blank_lines = options.as_ref().and_then(|o| o.include_blank_lines).unwrap_or(true);

        let mut total_files = 0;
        let mut total_lines = 0;
        let mut code_lines = 0;
        let mut comment_lines = 0;
        let mut blank_lines = 0;
        let mut by_language = HashMap::new();

        if path.is_file() {
            if self.should_analyze_file(path, file_extensions) {
                self.analyze_file_stats(
                    path,
                    include_comments,
                    include_blank_lines,
                    &mut total_files,
                    &mut total_lines,
                    &mut code_lines,
                    &mut comment_lines,
                    &mut blank_lines,
                    &mut by_language,
                ).await?;
            }
        } else {
            self.analyze_directory_stats(
                path,
                recursive,
                file_extensions,
                include_comments,
                include_blank_lines,
                &mut total_files,
                &mut total_lines,
                &mut code_lines,
                &mut comment_lines,
                &mut blank_lines,
                &mut by_language,
            ).await?;
        }

        Ok(CodeStatistics {
            total_files,
            total_lines,
            code_lines,
            comment_lines,
            blank_lines,
            by_language,
        })
    }

    /// 检查是否应该分析该文件
    fn should_analyze_file(&self, file_path: &Path, file_extensions: &Option<Vec<String>>) -> bool {
        if let Some(extensions) = file_extensions {
            if let Some(ext) = file_path.extension().and_then(|e| e.to_str()) {
                extensions.contains(&ext.to_string())
            } else {
                false
            }
        } else {
            // 默认分析常见的代码文件
            if let Some(ext) = file_path.extension().and_then(|e| e.to_str()) {
                matches!(ext, "rs" | "py" | "js" | "ts" | "java" | "cpp" | "c" | "h" | "go" | "rb" | "php" | "cs" | "swift" | "kt")
            } else {
                false
            }
        }
    }

    /// 分析文件统计
    async fn analyze_file_stats(
        &self,
        file_path: &Path,
        include_comments: bool,
        include_blank_lines: bool,
        total_files: &mut usize,
        total_lines: &mut usize,
        code_lines: &mut usize,
        comment_lines: &mut usize,
        blank_lines: &mut usize,
        by_language: &mut HashMap<String, LanguageStats>,
    ) -> ToolResult<()> {
        let content = fs::read_to_string(file_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read file: {}", e)))?;

        let lines: Vec<&str> = content.lines().collect();
        let file_total_lines = lines.len();
        let mut file_code_lines = 0;
        let mut file_comment_lines = 0;
        let mut file_blank_lines = 0;

        let language = self.get_language_from_extension(file_path);

        for line in lines {
            let trimmed = line.trim();
            if trimmed.is_empty() {
                if include_blank_lines {
                    file_blank_lines += 1;
                }
            } else if self.is_comment_line(trimmed, &language) {
                if include_comments {
                    file_comment_lines += 1;
                }
            } else {
                file_code_lines += 1;
            }
        }

        *total_files += 1;
        *total_lines += file_total_lines;
        *code_lines += file_code_lines;
        *comment_lines += file_comment_lines;
        *blank_lines += file_blank_lines;

        let lang_stats = by_language.entry(language).or_insert(LanguageStats {
            files: 0,
            lines: 0,
            code_lines: 0,
            comment_lines: 0,
        });

        lang_stats.files += 1;
        lang_stats.lines += file_total_lines;
        lang_stats.code_lines += file_code_lines;
        lang_stats.comment_lines += file_comment_lines;

        Ok(())
    }

    /// 分析目录统计
    async fn analyze_directory_stats(
        &self,
        dir_path: &Path,
        recursive: bool,
        file_extensions: &Option<Vec<String>>,
        include_comments: bool,
        include_blank_lines: bool,
        total_files: &mut usize,
        total_lines: &mut usize,
        code_lines: &mut usize,
        comment_lines: &mut usize,
        blank_lines: &mut usize,
        by_language: &mut HashMap<String, LanguageStats>,
    ) -> ToolResult<()> {
        let mut entries = fs::read_dir(dir_path).await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read directory: {}", e)))?;

        while let Some(entry) = entries.next_entry().await
            .map_err(|e| ToolError::ExecutionError(format!("Failed to read directory entry: {}", e)))? {
            
            let path = entry.path();
            
            if path.is_file() {
                if self.should_analyze_file(&path, file_extensions) {
                    self.analyze_file_stats(
                        &path,
                        include_comments,
                        include_blank_lines,
                        total_files,
                        total_lines,
                        code_lines,
                        comment_lines,
                        blank_lines,
                        by_language,
                    ).await?;
                }
            } else if path.is_dir() && recursive {
                // 跳过隐藏目录和常见的忽略目录
                if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                    if !dir_name.starts_with('.') && !matches!(dir_name, "node_modules" | "target" | "build" | "dist" | "__pycache__") {
                        self.analyze_directory_stats(
                            &path,
                            recursive,
                            file_extensions,
                            include_comments,
                            include_blank_lines,
                            total_files,
                            total_lines,
                            code_lines,
                            comment_lines,
                            blank_lines,
                            by_language,
                        ).await?;
                    }
                }
            }
        }

        Ok(())
    }

    /// 获取文件语言类型
    fn get_language_from_extension(&self, file_path: &Path) -> String {
        if let Some(ext) = file_path.extension().and_then(|e| e.to_str()) {
            match ext {
                "rs" => "Rust".to_string(),
                "py" => "Python".to_string(),
                "js" => "JavaScript".to_string(),
                "ts" => "TypeScript".to_string(),
                "java" => "Java".to_string(),
                "cpp" | "cc" | "cxx" => "C++".to_string(),
                "c" => "C".to_string(),
                "h" | "hpp" => "C/C++ Header".to_string(),
                "go" => "Go".to_string(),
                "rb" => "Ruby".to_string(),
                "php" => "PHP".to_string(),
                "cs" => "C#".to_string(),
                "swift" => "Swift".to_string(),
                "kt" => "Kotlin".to_string(),
                _ => format!("{} files", ext.to_uppercase()),
            }
        } else {
            "Unknown".to_string()
        }
    }

    /// 检查是否为注释行
    fn is_comment_line(&self, line: &str, language: &str) -> bool {
        match language {
            "Rust" | "JavaScript" | "TypeScript" | "Java" | "C++" | "C" | "C/C++ Header" | "Go" | "C#" | "Swift" | "Kotlin" => {
                line.starts_with("//") || line.starts_with("/*") || line.starts_with("*")
            }
            "Python" | "Ruby" => {
                line.starts_with("#")
            }
            "PHP" => {
                line.starts_with("//") || line.starts_with("#") || line.starts_with("/*")
            }
            _ => false,
        }
    }

    /// 简化的复杂度分析（实际实现会更复杂）
    async fn analyze_complexity(&self, _path: &Path, _recursive: bool, _file_extensions: &Option<Vec<String>>) -> ToolResult<ComplexityAnalysis> {
        // 这里是一个简化的实现，实际的复杂度分析需要解析 AST
        Ok(ComplexityAnalysis {
            average_complexity: 2.5,
            max_complexity: 8,
            complexity_distribution: HashMap::from([
                ("Low (1-3)".to_string(), 15),
                ("Medium (4-6)".to_string(), 8),
                ("High (7-10)".to_string(), 3),
                ("Very High (>10)".to_string(), 1),
            ]),
            high_complexity_functions: vec![],
        })
    }

    /// 简化的结构分析
    async fn analyze_structure(&self, _path: &Path, _recursive: bool, _file_extensions: &Option<Vec<String>>) -> ToolResult<StructureAnalysis> {
        // 这里是一个简化的实现
        Ok(StructureAnalysis {
            modules: 5,
            classes: 12,
            functions: 45,
            average_function_length: 15.2,
            longest_functions: vec![],
        })
    }

    /// 简化的质量分析
    async fn analyze_quality(&self, _path: &Path, _recursive: bool, _file_extensions: &Option<Vec<String>>, _options: &Option<AnalysisOptions>) -> ToolResult<QualityAnalysis> {
        // 这里是一个简化的实现
        Ok(QualityAnalysis {
            quality_score: 85.5,
            issues: vec![],
            suggestions: vec![
                "Consider adding more documentation".to_string(),
                "Some functions are too long and could be refactored".to_string(),
            ],
        })
    }

    /// 简化的依赖分析
    async fn analyze_dependencies(&self, _path: &Path, _recursive: bool, _file_extensions: &Option<Vec<String>>) -> ToolResult<DependencyAnalysis> {
        // 这里是一个简化的实现
        Ok(DependencyAnalysis {
            external_dependencies: vec![
                "tokio".to_string(),
                "serde".to_string(),
                "clap".to_string(),
            ],
            internal_dependencies: HashMap::new(),
            circular_dependencies: vec![],
        })
    }
}
